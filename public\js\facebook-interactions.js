// Enhanced Facebook-style Interactions

// Initialize Facebook-style interactions
document.addEventListener('DOMContentLoaded', function() {
  initializeFacebookInteractions();
  initializeTimeAgo();
  initializeCommentInputs();
});

/**
 * Initialize Facebook-style interactions
 */
function initializeFacebookInteractions() {
  // Add ripple effect to action buttons
  document.querySelectorAll('.fb-post-action-button').forEach(button => {
    button.addEventListener('click', createRippleEffect);
  });

  // Initialize like button states
  updateLikeButtonStates();

  // Initialize auto-resize for comment inputs
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    autoResizeTextarea(textarea);
  });
}

/**
 * Create ripple effect on button click
 */
function createRippleEffect(e) {
  const button = e.currentTarget;
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = e.clientX - rect.left - size / 2;
  const y = e.clientY - rect.top - size / 2;

  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
  `;

  button.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

/**
 * Handle like button click
 */
async function handleLike(postId) {
  const likeButton = document.querySelector(`[data-item-id="${postId}"].like`);
  const isLiked = likeButton.classList.contains('liked');

  try {
    // Optimistic UI update
    toggleLikeButton(likeButton, !isLiked);
    updateLikeCount(postId, !isLiked);

    // Create like animation
    if (!isLiked) {
      createLikeAnimation(likeButton);
    }

    // Make API call
    const response = await fetch(`/api/uploads/${postId}/like`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to like post');
    }

    const result = await response.json();
    
    // Update UI with server response
    updatePostStats(postId, result);

  } catch (error) {
    console.error('Error liking post:', error);
    
    // Revert optimistic update on error
    toggleLikeButton(likeButton, isLiked);
    updateLikeCount(postId, isLiked);
    
    showNotification('Failed to like post. Please try again.', 'error');
  }
}

/**
 * Toggle like button state
 */
function toggleLikeButton(button, isLiked) {
  if (isLiked) {
    button.classList.add('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up-fill';
  } else {
    button.classList.remove('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up';
  }
}

/**
 * Update like count display
 */
function updateLikeCount(postId, increment) {
  const statsDiv = document.querySelector(`[data-post-id="${postId}"] .fb-post-stats > div:first-child`);
  const countSpan = statsDiv.querySelector('span');
  
  if (countSpan) {
    const currentCount = parseInt(countSpan.textContent) || 0;
    const newCount = increment ? currentCount + 1 : Math.max(0, currentCount - 1);
    
    if (newCount > 0) {
      countSpan.textContent = newCount;
      if (!statsDiv.querySelector('i')) {
        statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>' + newCount + '</span>';
      }
    } else {
      statsDiv.innerHTML = '';
    }
  } else if (increment) {
    statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>1</span>';
  }
}

/**
 * Create like animation
 */
function createLikeAnimation(button) {
  const animation = document.createElement('div');
  animation.className = 'like-animation';
  animation.innerHTML = '<i class="bi bi-heart-fill" style="color: #e74c3c; font-size: 24px;"></i>';
  
  button.style.position = 'relative';
  button.appendChild(animation);
  
  setTimeout(() => {
    animation.remove();
  }, 700);
}

/**
 * Focus comment input
 */
function focusCommentInput(postId) {
  const commentInput = document.getElementById(`comment-text-${postId}`);
  if (commentInput) {
    commentInput.focus();
    commentInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

/**
 * Handle comment submission
 */
async function submitComment(postId) {
  const commentInput = document.getElementById(`comment-text-${postId}`);
  const commentText = commentInput.value.trim();

  if (!commentText) {
    showNotification('Please enter a comment', 'warning');
    return;
  }

  try {
    // Disable input during submission
    commentInput.disabled = true;

    const response = await fetch(`/api/uploads/${postId}/comment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: commentText })
    });

    if (!response.ok) {
      throw new Error('Failed to add comment');
    }

    const result = await response.json();
    
    // Clear input
    commentInput.value = '';
    autoResizeTextarea(commentInput);
    
    // Add comment to UI
    addCommentToUI(postId, result.comment);
    
    // Update comment count
    updateCommentCount(postId, 1);
    
    showNotification('Comment added successfully!', 'success');

  } catch (error) {
    console.error('Error adding comment:', error);
    showNotification('Failed to add comment. Please try again.', 'error');
  } finally {
    commentInput.disabled = false;
  }
}

/**
 * Handle comment keydown (Enter to submit)
 */
function handleCommentKeydown(event, postId) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    submitComment(postId);
  }
}

/**
 * Auto-resize textarea
 */
function autoResizeTextarea(textarea) {
  textarea.style.height = 'auto';
  textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
}

/**
 * Initialize comment inputs
 */
function initializeCommentInputs() {
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    textarea.addEventListener('input', function() {
      autoResizeTextarea(this);
    });
  });
}

/**
 * Handle share button click
 */
function handleShare(postId) {
  // Create share modal or use Web Share API
  if (navigator.share) {
    const post = document.querySelector(`[data-post-id="${postId}"]`);
    const title = post.querySelector('.fb-post-text h5')?.textContent || 'Sustainable Farming Post';
    const text = post.querySelector('.fb-post-text p')?.textContent || '';
    
    navigator.share({
      title: title,
      text: text,
      url: window.location.href + '#post-' + postId
    }).catch(console.error);
  } else {
    // Fallback: Copy link to clipboard
    const url = window.location.href + '#post-' + postId;
    navigator.clipboard.writeText(url).then(() => {
      showNotification('Link copied to clipboard!', 'success');
    }).catch(() => {
      showNotification('Failed to copy link', 'error');
    });
  }
}

/**
 * Initialize time ago functionality
 */
function initializeTimeAgo() {
  updateTimeAgo();
  setInterval(updateTimeAgo, 60000); // Update every minute
}

/**
 * Update time ago displays
 */
function updateTimeAgo() {
  document.querySelectorAll('.time-ago').forEach(element => {
    const time = element.getAttribute('data-time');
    if (time) {
      element.textContent = getTimeAgo(new Date(time));
    }
  });
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
  
  return date.toLocaleDateString();
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
  notification.style.cssText = `
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
  `;
  
  const icon = type === 'success' ? 'check-circle-fill' : 
               type === 'error' ? 'exclamation-triangle-fill' : 
               'info-circle-fill';
  
  notification.innerHTML = `
    <div class="d-flex align-items-center">
      <i class="bi bi-${icon} me-2"></i>
      <span>${message}</span>
      <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);
