import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import { getCommodities, getMarkets, getMarketPrices } from '../services/marketDataService.js';
import { exportToCSV, exportToJSON, exportToExcel } from '../services/exportService.js';
import { analyzeTrend, analyzeSeasonality, analyzeVolatility, generateForecast } from '../services/analysisService.js';
import { getFirestore } from 'firebase-admin/firestore';

const router = express.Router();

// Main market trends page
router.get('/', isAuthenticated, async (req, res) => {
  try {
    res.render('market-trends/index', {
      user: req.user,
      originalUrl: req.originalUrl,
      title: 'Market Trends - Crop Prices',
      isAuthenticated: true
    });
  } catch (error) {
    console.error('Error loading market trends page:', error);
    res.status(500).render('error', {
      message: 'Error loading market trends page',
      error: error
    });
  }
});

// API endpoint to get market data
router.get('/api/data', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange } = req.query;

    // Fetch real market data from our service
    const marketData = await getMarketPrices(crop, location, timeRange);

    res.json(marketData);
  } catch (error) {
    console.error('Error fetching market data:', error);
    res.status(500).json({ error: 'Failed to fetch market data' });
  }
});

// API endpoint to get available crops
router.get('/api/crops', isAuthenticated, async (req, res) => {
  try {
    // Fetch real commodity data from our service
    const crops = await getCommodities();

    res.json(crops);
  } catch (error) {
    console.error('Error fetching crops:', error);
    res.status(500).json({ error: 'Failed to fetch crops' });
  }
});

// API endpoint to get available market locations
router.get('/api/locations', isAuthenticated, async (req, res) => {
  try {
    // Fetch real market data from our service
    const locations = await getMarkets();

    res.json(locations);
  } catch (error) {
    console.error('Error fetching locations:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// API endpoint to save user price alerts
router.post('/api/alerts', isAuthenticated, async (req, res) => {
  try {
    const { crop, price, condition } = req.body;
    const userId = req.user.uid;

    // Save to Firestore
    const db = getFirestore();
    const alertRef = db.collection('users').doc(userId).collection('priceAlerts').doc();

    await alertRef.set({
      id: alertRef.id,
      crop,
      price: parseFloat(price),
      condition,
      createdAt: new Date().toISOString(),
      userId
    });

    res.json({
      success: true,
      message: 'Alert saved successfully',
      alert: {
        id: alertRef.id,
        crop,
        price: parseFloat(price),
        condition,
        createdAt: new Date().toISOString(),
        userId
      }
    });
  } catch (error) {
    console.error('Error saving price alert:', error);
    res.status(500).json({ error: 'Failed to save price alert' });
  }
});

// API endpoint to get user price alerts
router.get('/api/alerts', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user.uid;

    // Get from Firestore
    const db = getFirestore();
    const alertsSnapshot = await db.collection('users').doc(userId).collection('priceAlerts').get();

    const alerts = [];
    alertsSnapshot.forEach(doc => {
      alerts.push(doc.data());
    });

    res.json(alerts);
  } catch (error) {
    console.error('Error fetching price alerts:', error);
    res.status(500).json({ error: 'Failed to fetch price alerts' });
  }
});

// API endpoint to delete a user price alert
router.delete('/api/alerts/:alertId', isAuthenticated, async (req, res) => {
  try {
    const { alertId } = req.params;
    const userId = req.user.uid;

    // Delete from Firestore
    const db = getFirestore();
    await db.collection('users').doc(userId).collection('priceAlerts').doc(alertId).delete();

    res.json({
      success: true,
      message: 'Alert deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting price alert:', error);
    res.status(500).json({ error: 'Failed to delete price alert' });
  }
});

// API endpoint to save user preferences
router.post('/api/preferences', isAuthenticated, async (req, res) => {
  try {
    const { defaultCrop, defaultLocation, defaultTimeRange, chartType, theme } = req.body;
    const userId = req.user.uid;

    // Save to Firestore
    const db = getFirestore();
    await db.collection('users').doc(userId).set({
      marketPreferences: {
        defaultCrop,
        defaultLocation,
        defaultTimeRange,
        chartType,
        theme
      }
    }, { merge: true });

    res.json({
      success: true,
      message: 'Preferences saved successfully',
      preferences: { defaultCrop, defaultLocation, defaultTimeRange, chartType, theme }
    });
  } catch (error) {
    console.error('Error saving preferences:', error);
    res.status(500).json({ error: 'Failed to save preferences' });
  }
});

// API endpoint to get user preferences
router.get('/api/preferences', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user.uid;

    // Get from Firestore
    const db = getFirestore();
    const userDoc = await db.collection('users').doc(userId).get();

    if (userDoc.exists) {
      const userData = userDoc.data();
      res.json(userData.marketPreferences || {
        defaultCrop: 'all',
        defaultLocation: 'all',
        defaultTimeRange: '7days',
        chartType: 'line',
        theme: 'light'
      });
    } else {
      res.json({
        defaultCrop: 'all',
        defaultLocation: 'all',
        defaultTimeRange: '7days',
        chartType: 'line',
        theme: 'light'
      });
    }
  } catch (error) {
    console.error('Error fetching preferences:', error);
    res.status(500).json({ error: 'Failed to fetch preferences' });
  }
});

// API endpoint to export data as CSV
router.get('/api/export/csv', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, chartDataOnly, includeMetadata, includeStatistics } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Export options
    const options = {
      chartDataOnly: chartDataOnly === 'true',
      includeMetadata: includeMetadata === 'true',
      includeStatistics: includeStatistics === 'true',
      filters: { crop, location, timeRange }
    };

    // Export to CSV
    const csv = exportToCSV(marketData, options);

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=market-trends-export.csv`);

    // Send the CSV data
    res.send(csv);
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    res.status(500).json({ error: 'Failed to export data to CSV' });
  }
});

// API endpoint to export data as JSON
router.get('/api/export/json', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, chartDataOnly, includeMetadata, includeStatistics } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Export options
    const options = {
      chartDataOnly: chartDataOnly === 'true',
      includeMetadata: includeMetadata === 'true',
      includeStatistics: includeStatistics === 'true',
      filters: { crop, location, timeRange }
    };

    // Export to JSON
    const json = exportToJSON(marketData, options);

    // Set headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename=market-trends-export.json`);

    // Send the JSON data
    res.send(json);
  } catch (error) {
    console.error('Error exporting to JSON:', error);
    res.status(500).json({ error: 'Failed to export data to JSON' });
  }
});

// API endpoint to export data as Excel
router.get('/api/export/excel', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, chartDataOnly, includeMetadata, includeStatistics } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Export options
    const options = {
      chartDataOnly: chartDataOnly === 'true',
      includeMetadata: includeMetadata === 'true',
      includeStatistics: includeStatistics === 'true',
      filters: { crop, location, timeRange }
    };

    // Export to Excel
    const excelBuffer = await exportToExcel(marketData, options);

    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=market-trends-export.xlsx`);

    // Send the Excel data
    res.send(excelBuffer);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    res.status(500).json({ error: 'Failed to export data to Excel' });
  }
});

// API endpoint for trend analysis
router.get('/api/analysis/trend', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, windowSize } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Analysis options
    const options = {
      crop,
      location,
      windowSize: parseInt(windowSize) || 7
    };

    // Perform trend analysis
    const analysis = analyzeTrend(marketData, options);

    res.json(analysis);
  } catch (error) {
    console.error('Error performing trend analysis:', error);
    res.status(500).json({ error: 'Failed to perform trend analysis' });
  }
});

// API endpoint for seasonality analysis
router.get('/api/analysis/seasonality', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, seasonalPeriod } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Analysis options
    const options = {
      crop,
      location,
      seasonalPeriod: parseInt(seasonalPeriod) || 7
    };

    // Perform seasonality analysis
    const analysis = analyzeSeasonality(marketData, options);

    res.json(analysis);
  } catch (error) {
    console.error('Error performing seasonality analysis:', error);
    res.status(500).json({ error: 'Failed to perform seasonality analysis' });
  }
});

// API endpoint for volatility analysis
router.get('/api/analysis/volatility', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, windowSize } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Analysis options
    const options = {
      crop,
      location,
      windowSize: parseInt(windowSize) || 7
    };

    // Perform volatility analysis
    const analysis = analyzeVolatility(marketData, options);

    res.json(analysis);
  } catch (error) {
    console.error('Error performing volatility analysis:', error);
    res.status(500).json({ error: 'Failed to perform volatility analysis' });
  }
});

// API endpoint for price forecast
router.get('/api/analysis/forecast', isAuthenticated, async (req, res) => {
  try {
    const { crop, location, timeRange, forecastHorizon, confidenceLevel } = req.query;

    // Fetch market data
    const marketData = await getMarketPrices(crop, location, timeRange);

    // Forecast options
    const options = {
      crop,
      location,
      forecastHorizon: parseInt(forecastHorizon) || 7,
      confidenceLevel: parseFloat(confidenceLevel) || 0.95
    };

    // Generate forecast
    const forecast = generateForecast(marketData, options);

    res.json(forecast);
  } catch (error) {
    console.error('Error generating forecast:', error);
    res.status(500).json({ error: 'Failed to generate forecast' });
  }
});

// Helper function to generate mock market data
function generateMarketData(crop = 'all', location = 'all', timeRange = '7days') {
  const crops = crop === 'all'
    ? ['rice', 'wheat', 'corn', 'soybeans', 'potatoes', 'tomatoes', 'onions', 'cotton']
    : [crop];

  const locations = location === 'all'
    ? ['new-york', 'chicago', 'los-angeles', 'houston', 'miami', 'seattle', 'denver', 'atlanta']
    : [location];

  // Determine number of data points based on time range
  let dataPoints;
  switch(timeRange) {
    case '1day': dataPoints = 24; break;
    case '7days': dataPoints = 7; break;
    case '30days': dataPoints = 30; break;
    case '90days': dataPoints = 90; break;
    default: dataPoints = 7;
  }

  // Generate data for each crop and location
  const result = [];

  crops.forEach(cropName => {
    locations.forEach(locationName => {
      // Base price for this crop (different for each crop)
      const basePrice = getBasePriceForCrop(cropName);

      // Generate time series data
      const timeSeriesData = [];
      const today = new Date();

      for (let i = 0; i < dataPoints; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - (dataPoints - i - 1));

        // Add some randomness to the price
        const randomFactor = 0.9 + (Math.random() * 0.2); // Between 0.9 and 1.1
        const price = basePrice * randomFactor;

        timeSeriesData.push({
          date: date.toISOString().split('T')[0],
          price: parseFloat(price.toFixed(2))
        });
      }

      // Calculate price change percentage
      const firstPrice = timeSeriesData[0].price;
      const lastPrice = timeSeriesData[timeSeriesData.length - 1].price;
      const priceChange = ((lastPrice - firstPrice) / firstPrice) * 100;

      result.push({
        crop: cropName,
        location: locationName,
        currentPrice: lastPrice,
        priceChange: parseFloat(priceChange.toFixed(2)),
        lastUpdated: new Date().toISOString(),
        timeSeriesData: timeSeriesData
      });
    });
  });

  return result;
}

// Helper function to get base price for a crop
function getBasePriceForCrop(crop) {
  const prices = {
    'rice': 15.50,
    'wheat': 7.25,
    'corn': 4.75,
    'soybeans': 14.30,
    'potatoes': 12.80,
    'tomatoes': 25.40,
    'onions': 18.60,
    'cotton': 85.20
  };

  return prices[crop] || 10.00; // Default price if crop not found
}

export default router;
