import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '../config/initFirebase.js';
import { getCurrentUser } from './firebaseService.js';

// Collection names
const COURSES_COLLECTION = 'courses';
const MODULES_COLLECTION = 'modules';
const USERS_COLLECTION = 'users';
const PROGRESS_COLLECTION = 'courseProgress';

/**
 * Get all published courses
 */
export const getAllCourses = async () => {
  try {
    // Modified to get all courses regardless of published status
    const coursesQuery = query(
      collection(db, COURSES_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    const coursesSnapshot = await getDocs(coursesQuery);

    const courses = [];
    coursesSnapshot.forEach((doc) => {
      courses.push({
        id: doc.id,
        ...doc.data()
      });
    });

    console.log(`Retrieved ${courses.length} courses from Firestore:`,
      courses.map(c => `${c.title} (Published: ${c.isPublished})`));

    return courses;
  } catch (error) {
    console.error('Error getting courses:', error);

    // Return sample courses for development/demo
    return getSampleCourses();
  }
};

/**
 * Get courses by category
 */
export const getCoursesByCategory = async (category) => {
  try {
    const coursesQuery = query(
      collection(db, COURSES_COLLECTION),
      where('category', '==', category),
      where('isPublished', '==', true),
      orderBy('createdAt', 'desc')
    );

    const coursesSnapshot = await getDocs(coursesQuery);

    const courses = [];
    coursesSnapshot.forEach((doc) => {
      courses.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return courses;
  } catch (error) {
    console.error(`Error getting courses for category ${category}:`, error);

    // Return sample courses for the category for development/demo
    return getSampleCoursesByCategory(category);
  }
};

/**
 * Get a single course by ID
 */
export const getCourseById = async (courseId) => {
  try {
    const courseDoc = await getDoc(doc(db, COURSES_COLLECTION, courseId));

    if (!courseDoc.exists()) {
      return null;
    }

    return {
      id: courseDoc.id,
      ...courseDoc.data()
    };
  } catch (error) {
    console.error(`Error getting course ${courseId}:`, error);

    // Return a sample course for development/demo
    return getSampleCourseById(courseId);
  }
};

/**
 * Get all modules for a course
 */
export const getCourseModules = async (courseId) => {
  try {
    const modulesQuery = query(
      collection(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION),
      where('isPublished', '==', true),
      orderBy('order', 'asc')
    );

    const modulesSnapshot = await getDocs(modulesQuery);

    const modules = [];
    modulesSnapshot.forEach((doc) => {
      modules.push({
        id: doc.id,
        courseId,
        ...doc.data()
      });
    });

    return modules;
  } catch (error) {
    console.error(`Error getting modules for course ${courseId}:`, error);

    // Return sample modules for development/demo
    return getSampleModules(courseId);
  }
};

/**
 * Get a single module by ID
 */
export const getModuleById = async (courseId, moduleId) => {
  try {
    const moduleDoc = await getDoc(
      doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleId)
    );

    if (!moduleDoc.exists()) {
      return null;
    }

    return {
      id: moduleDoc.id,
      courseId,
      ...moduleDoc.data()
    };
  } catch (error) {
    console.error(`Error getting module ${moduleId}:`, error);

    // Return a sample module for development/demo
    return getSampleModuleById(courseId, moduleId);
  }
};

/**
 * Enroll a user in a course
 */
export const enrollInCourse = async (courseId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to enroll in a course');
    }

    // Check if the course exists
    const course = await getCourseById(courseId);

    if (!course) {
      throw new Error('Course not found');
    }

    // Check if user is already enrolled
    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (progressDoc.exists()) {
      // User is already enrolled, just update the lastAccessedAt
      await updateDoc(progressRef, {
        lastAccessedAt: serverTimestamp()
      });

      return {
        courseId,
        userId: user.uid,
        ...progressDoc.data()
      };
    }

    // Get the first module of the course
    const modules = await getCourseModules(courseId);
    const firstModuleId = modules.length > 0 ? modules[0].id : null;

    // Create new progress document
    const progressData = {
      courseId,
      userId: user.uid,
      enrolledAt: serverTimestamp(),
      lastAccessedAt: serverTimestamp(),
      completedModules: [],
      currentModuleId: firstModuleId,
      progress: 0,
      isCompleted: false,
      notes: ''
    };

    await setDoc(progressRef, progressData);

    // Increment the enrollment count for the course
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    await updateDoc(courseRef, {
      enrollmentCount: increment(1)
    });

    return {
      courseId,
      userId: user.uid,
      ...progressData
    };
  } catch (error) {
    console.error(`Error enrolling in course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Get user's progress for a course
 */
export const getCourseProgress = async (courseId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      return null;
    }

    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      return null;
    }

    return {
      courseId,
      userId: user.uid,
      ...progressDoc.data()
    };
  } catch (error) {
    console.error(`Error getting progress for course ${courseId}:`, error);
    return null;
  }
};

/**
 * Mark a module as completed
 */
export const completeModule = async (courseId, moduleId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to track progress');
    }

    // Get the course and all modules
    const course = await getCourseById(courseId);
    const modules = await getCourseModules(courseId);

    if (!course || modules.length === 0) {
      throw new Error('Course or modules not found');
    }

    // Get current progress
    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      throw new Error('You are not enrolled in this course');
    }

    const progress = progressDoc.data();

    // Check if module is already completed
    if (progress.completedModules.includes(moduleId)) {
      return {
        courseId,
        userId: user.uid,
        ...progress
      };
    }

    // Add module to completed modules
    const completedModules = [...progress.completedModules, moduleId];

    // Calculate new progress percentage
    const progressPercentage = Math.round((completedModules.length / modules.length) * 100);

    // Determine if course is completed
    const isCompleted = progressPercentage === 100;

    // Find the next module
    let nextModuleId = null;
    if (!isCompleted) {
      const currentModuleIndex = modules.findIndex(m => m.id === moduleId);
      if (currentModuleIndex < modules.length - 1) {
        nextModuleId = modules[currentModuleIndex + 1].id;
      }
    }

    // Update progress
    const updateData = {
      completedModules,
      progress: progressPercentage,
      lastAccessedAt: serverTimestamp()
    };

    if (nextModuleId) {
      updateData.currentModuleId = nextModuleId;
    }

    if (isCompleted) {
      updateData.isCompleted = true;
      updateData.completedAt = serverTimestamp();
    }

    await updateDoc(progressRef, updateData);

    return {
      courseId,
      userId: user.uid,
      ...progress,
      ...updateData,
      completedModules
    };
  } catch (error) {
    console.error(`Error completing module ${moduleId}:`, error);
    throw error;
  }
};

/**
 * Get all courses a user is enrolled in
 */
export const getUserCourses = async () => {
  try {
    const user = getCurrentUser();

    if (!user) {
      return [];
    }

    const progressQuery = query(
      collection(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION),
      orderBy('lastAccessedAt', 'desc')
    );

    const progressSnapshot = await getDocs(progressQuery);

    const coursePromises = [];
    progressSnapshot.forEach((doc) => {
      const progress = doc.data();
      coursePromises.push(
        getCourseById(progress.courseId).then(course => ({
          ...course,
          progress: progress.progress,
          isCompleted: progress.isCompleted,
          currentModuleId: progress.currentModuleId,
          lastAccessedAt: progress.lastAccessedAt
        }))
      );
    });

    const courses = await Promise.all(coursePromises);
    return courses.filter(course => course !== null);
  } catch (error) {
    console.error('Error getting user courses:', error);
    return [];
  }
};

/**
 * Save user notes for a course
 */
export const saveUserNotes = async (courseId, notes) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to save notes');
    }

    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      throw new Error('You are not enrolled in this course');
    }

    await updateDoc(progressRef, {
      notes,
      lastAccessedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error(`Error saving notes for course ${courseId}:`, error);
    throw error;
  }
};

// Sample data functions for development/demo

function getSampleCourses() {
  return [
    {
      id: 'course-organic-farming-101',
      title: 'Organic Farming 101',
      description: 'Learn the fundamentals of organic farming and how to transition from conventional methods.',
      category: 'organic-farming',
      imageUrl: '/img/courses/organic-farming-101.jpg',
      authorName: 'Jane Smith',
      level: 'beginner',
      duration: 240,
      moduleCount: 5,
      enrollmentCount: 128,
      isPublished: true,
      isPremium: false,
      price: 0,
      currency: 'USD',
      createdAt: new Date().toISOString(),
      tags: ['organic', 'beginner', 'certification'],
      objectives: [
        'Understand organic farming principles',
        'Learn about certification requirements',
        'Develop a transition plan'
      ]
    },
    {
      id: 'course-water-conservation',
      title: 'Water Conservation Techniques',
      description: 'Discover effective methods to conserve water in your farming operations.',
      category: 'water-conservation',
      imageUrl: '/img/courses/water-conservation.jpg',
      authorName: 'Michael Johnson',
      level: 'intermediate',
      duration: 180,
      moduleCount: 4,
      enrollmentCount: 95,
      isPublished: true,
      isPremium: true,
      price: 49.99,
      currency: 'USD',
      createdAt: new Date().toISOString(),
      tags: ['water', 'conservation', 'irrigation', 'premium'],
      objectives: [
        'Implement efficient irrigation systems',
        'Reduce water waste in farming operations',
        'Select drought-resistant crops'
      ]
    },
    {
      id: 'course-soil-health',
      title: 'Building Healthy Soil',
      description: 'Learn how to improve and maintain soil health for sustainable farming.',
      category: 'soil-health',
      imageUrl: '/img/courses/soil-health.jpg',
      authorName: 'Dr. Robert Chen',
      level: 'intermediate',
      duration: 210,
      moduleCount: 6,
      enrollmentCount: 156,
      isPublished: true,
      isPremium: false,
      price: 0,
      currency: 'USD',
      createdAt: new Date().toISOString(),
      tags: ['soil', 'compost', 'fertility'],
      objectives: [
        'Understand soil composition and health indicators',
        'Implement composting techniques',
        'Develop crop rotation plans for soil health'
      ]
    },
    {
      id: 'course-advanced-hydroponics',
      title: 'Advanced Hydroponics Mastery',
      description: 'Master advanced hydroponic systems and maximize your crop yields with cutting-edge techniques.',
      category: 'hydroponics',
      imageUrl: '/img/courses/advanced-hydroponics.jpg',
      authorName: 'Dr. Sarah Martinez',
      level: 'advanced',
      duration: 360,
      moduleCount: 12,
      enrollmentCount: 67,
      isPublished: true,
      isPremium: true,
      price: 149.99,
      currency: 'USD',
      createdAt: new Date().toISOString(),
      tags: ['hydroponics', 'advanced', 'premium', 'technology'],
      objectives: [
        'Design and build advanced hydroponic systems',
        'Optimize nutrient solutions for maximum yield',
        'Implement automated monitoring systems',
        'Troubleshoot complex hydroponic issues'
      ]
    },
    {
      id: 'course-precision-agriculture',
      title: 'Precision Agriculture with AI & IoT',
      description: 'Learn to use artificial intelligence and IoT sensors to optimize farming operations.',
      category: 'technology',
      imageUrl: '/img/courses/precision-agriculture.jpg',
      authorName: 'Prof. David Kim',
      level: 'advanced',
      duration: 480,
      moduleCount: 16,
      enrollmentCount: 34,
      isPublished: true,
      isPremium: true,
      price: 299.99,
      currency: 'USD',
      createdAt: new Date().toISOString(),
      tags: ['ai', 'iot', 'technology', 'premium', 'advanced'],
      objectives: [
        'Implement IoT sensor networks on farms',
        'Use AI for crop monitoring and prediction',
        'Optimize resource allocation with data analytics',
        'Build smart farming automation systems'
      ]
    }
  ];
}

function getSampleCoursesByCategory(category) {
  const allCourses = getSampleCourses();
  return allCourses.filter(course => course.category === category);
}

function getSampleCourseById(courseId) {
  const allCourses = getSampleCourses();
  return allCourses.find(course => course.id === courseId) || null;
}

function getSampleModules(courseId) {
  if (courseId === 'course-organic-farming-101') {
    return [
      {
        id: 'module-1',
        courseId,
        title: 'Introduction to Organic Farming',
        description: 'Learn the basic principles and benefits of organic farming.',
        order: 1,
        duration: 45,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-2',
        courseId,
        title: 'Soil Management for Organic Farms',
        description: 'Discover techniques for building and maintaining healthy soil without synthetic inputs.',
        order: 2,
        duration: 60,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-3',
        courseId,
        title: 'Natural Pest Control',
        description: 'Learn how to manage pests using natural and biological methods.',
        order: 3,
        duration: 50,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-4',
        courseId,
        title: 'Organic Certification Process',
        description: 'Understand the requirements and steps for obtaining organic certification.',
        order: 4,
        duration: 40,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-5',
        courseId,
        title: 'Marketing Organic Products',
        description: 'Learn strategies for marketing and selling your organic produce.',
        order: 5,
        duration: 45,
        isPublished: true,
        createdAt: new Date().toISOString()
      }
    ];
  } else if (courseId === 'course-water-conservation') {
    return [
      {
        id: 'module-1',
        courseId,
        title: 'Water Usage in Agriculture',
        description: 'Understand current water usage patterns and challenges in agriculture.',
        order: 1,
        duration: 40,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-2',
        courseId,
        title: 'Efficient Irrigation Systems',
        description: 'Learn about drip irrigation, sprinklers, and other efficient watering methods.',
        order: 2,
        duration: 50,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-3',
        courseId,
        title: 'Rainwater Harvesting',
        description: 'Discover techniques for collecting and storing rainwater for agricultural use.',
        order: 3,
        duration: 45,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-4',
        courseId,
        title: 'Drought-Resistant Farming',
        description: 'Learn about crop selection and farming practices for drought conditions.',
        order: 4,
        duration: 45,
        isPublished: true,
        createdAt: new Date().toISOString()
      }
    ];
  } else if (courseId === 'course-soil-health') {
    return [
      {
        id: 'module-1',
        courseId,
        title: 'Understanding Soil Composition',
        description: 'Learn about the components of healthy soil and how to assess soil quality.',
        order: 1,
        duration: 35,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-2',
        courseId,
        title: 'Composting Techniques',
        description: 'Discover different methods for creating nutrient-rich compost.',
        order: 2,
        duration: 40,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-3',
        courseId,
        title: 'Cover Crops and Green Manures',
        description: 'Learn how to use cover crops to protect and enhance soil.',
        order: 3,
        duration: 35,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-4',
        courseId,
        title: 'Crop Rotation Strategies',
        description: 'Understand how to plan crop rotations for optimal soil health.',
        order: 4,
        duration: 30,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-5',
        courseId,
        title: 'Soil Testing and Analysis',
        description: 'Learn how to test your soil and interpret the results.',
        order: 5,
        duration: 35,
        isPublished: true,
        createdAt: new Date().toISOString()
      },
      {
        id: 'module-6',
        courseId,
        title: 'Remediating Damaged Soil',
        description: 'Discover techniques for restoring soil that has been damaged by erosion or chemicals.',
        order: 6,
        duration: 35,
        isPublished: true,
        createdAt: new Date().toISOString()
      }
    ];
  }

  return [];
}

function getSampleModuleById(courseId, moduleId) {
  const modules = getSampleModules(courseId);
  const module = modules.find(m => m.id === moduleId);

  if (!module) {
    return null;
  }

  // Add sample content to the module
  return {
    ...module,
    content: `
      <h2>Sample Content for ${module.title}</h2>
      <p>This is sample content for the module. In a real application, this would contain detailed information about the topic.</p>
      <h3>Key Points</h3>
      <ul>
        <li>Important point 1 about ${module.title}</li>
        <li>Important point 2 about ${module.title}</li>
        <li>Important point 3 about ${module.title}</li>
      </ul>
      <p>The content would include detailed explanations, examples, and possibly images or diagrams to illustrate concepts.</p>
      <blockquote>
        <p>"A relevant quote about sustainable farming practices related to this module."</p>
        <footer>- Famous Agricultural Expert</footer>
      </blockquote>
    `,
    resources: [
      {
        name: 'Module Guide',
        description: 'A comprehensive guide covering all topics in this module',
        url: '#',
        type: 'pdf'
      },
      {
        name: 'Practice Worksheet',
        description: 'Exercises to help you apply what you\'ve learned',
        url: '#',
        type: 'doc'
      }
    ]
  };
}
