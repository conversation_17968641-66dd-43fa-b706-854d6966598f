/**
 * Simple authentication service using in-memory storage
 * This is for demonstration purposes only and should not be used in production
 */

// In-memory storage for users and current user
const inMemoryStorage = {
  users: [],
  currentUser: null
};

// Get all registered users
const getUsers = () => {
  return inMemoryStorage.users;
};

// Save users to in-memory storage
const saveUsers = (users) => {
  inMemoryStorage.users = users;
};

// Register a new user
export const registerUser = (userData) => {
  const users = getUsers();

  // Check if email already exists
  if (users.some(user => user.email === userData.email)) {
    throw new Error('Email already in use');
  }

  // Create user object with ID and creation time
  const newUser = {
    ...userData,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add user to array and save
  users.push(newUser);
  saveUsers(users);

  return newUser;
};

// Login user
export const loginUser = (email, password) => {
  const users = getUsers();

  // Find user by email and password
  const user = users.find(user => user.email === email && user.password === password);

  if (!user) {
    throw new Error('Invalid email or password');
  }

  // Store current user in localStorage
  setCurrentUser(user);

  return user;
};

// Set current user
export const setCurrentUser = (user) => {
  // Remove password before storing
  const { password, ...userWithoutPassword } = user;
  inMemoryStorage.currentUser = userWithoutPassword;
};

// Get current user
export const getCurrentUser = () => {
  return inMemoryStorage.currentUser;
};

// Logout user
export const logoutUser = () => {
  inMemoryStorage.currentUser = null;
};

// Check if user is authenticated
export const isAuthenticated = () => {
  return !!getCurrentUser();
};

// Update user profile
export const updateUserProfile = (userData) => {
  const users = getUsers();
  const currentUser = getCurrentUser();

  if (!currentUser) {
    throw new Error('No authenticated user');
  }

  // Find and update user
  const updatedUsers = users.map(user => {
    if (user.id === currentUser.id) {
      const updatedUser = {
        ...user,
        ...userData,
        updatedAt: new Date().toISOString()
      };

      // Update current user in localStorage
      setCurrentUser(updatedUser);

      return updatedUser;
    }
    return user;
  });

  saveUsers(updatedUsers);
  return getCurrentUser();
};

// Get user by ID
export const getUserById = (userId) => {
  const users = getUsers();
  return users.find(user => user.id === userId);
};

// Add a demo user for testing
registerUser({
  email: '<EMAIL>',
  password: 'password123',
  firstName: 'Test',
  lastName: 'User',
  displayName: 'Test User',
  location: 'Test Location',
  farmName: 'Test Farm',
  metadata: {
    creationTime: new Date().toISOString()
  }
});
