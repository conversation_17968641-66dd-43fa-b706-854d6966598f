// Script to add sample content to localStorage for testing
export const addSampleContent = () => {
  const sampleUploads = [
    {
      id: 'local-1703123456789',
      title: 'Sustainable Corn Farming Techniques',
      description: 'Learn about the latest sustainable corn farming methods that increase yield while protecting the environment. This comprehensive guide covers crop rotation, organic fertilizers, and water conservation.',
      category: 'farming-techniques',
      fileUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      fileType: 'image/jpeg',
      userId: 'local-user1',
      userName: 'John Farmer',
      userPhotoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-12-20T10:30:00Z').toISOString(),
      updatedAt: new Date('2024-12-20T10:30:00Z').toISOString(),
      likes: [],
      likeCount: 0,
      comments: [
        {
          id: 'comment-1',
          text: 'Great tips! I\'ve been looking for sustainable farming methods like this.',
          userId: 'local-user2',
          userName: 'Sarah Green',
          userPhotoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-12-20T11:00:00Z').toISOString()
        },
        {
          id: 'comment-2',
          text: 'How long does it take to see results with these methods?',
          userId: 'local-user3',
          userName: 'Mike Waters',
          userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-12-20T12:30:00Z').toISOString()
        }
      ],
      commentCount: 2
    },
    {
      id: 'local-1703123456790',
      title: 'Organic Tomato Harvest',
      description: 'Fresh organic tomatoes ready for harvest! These were grown using completely natural methods without any chemical pesticides or fertilizers.',
      category: 'crops',
      fileUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      fileType: 'image/jpeg',
      userId: 'local-user2',
      userName: 'Sarah Green',
      userPhotoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-12-19T15:45:00Z').toISOString(),
      updatedAt: new Date('2024-12-19T15:45:00Z').toISOString(),
      likes: ['local-user1'],
      likeCount: 1,
      comments: [
        {
          id: 'comment-1',
          text: 'Amazing harvest! How long did it take to grow?',
          userId: 'local-user1',
          userName: 'John Farmer',
          userPhotoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-12-19T16:00:00Z').toISOString()
        }
      ],
      commentCount: 1
    },
    {
      id: 'local-1703123456791',
      title: 'Water Conservation in Agriculture',
      description: 'Implementing drip irrigation systems can reduce water usage by up to 50% while maintaining crop yields. Here are the key benefits and installation tips.',
      category: 'farming-techniques',
      fileUrl: null,
      fileType: null,
      userId: 'local-user3',
      userName: 'Mike Waters',
      userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-12-18T09:15:00Z').toISOString(),
      updatedAt: new Date('2024-12-18T09:15:00Z').toISOString(),
      likes: ['local-user1', 'local-user2'],
      likeCount: 2,
      comments: [],
      commentCount: 0
    },
    {
      id: 'local-1703123456792',
      title: 'New Farming Equipment Review',
      description: 'Testing out the latest precision planting equipment. This new seeder has GPS guidance and variable rate technology for optimal seed placement.',
      category: 'others',
      fileUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      fileType: 'image/jpeg',
      userId: 'local-user1',
      userName: 'John Farmer',
      userPhotoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-12-17T14:20:00Z').toISOString(),
      updatedAt: new Date('2024-12-17T14:20:00Z').toISOString(),
      likes: [],
      likeCount: 0,
      comments: [
        {
          id: 'comment-2',
          text: 'Looks like great equipment! What\'s the price range?',
          userId: 'local-user2',
          userName: 'Sarah Green',
          userPhotoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-12-17T15:00:00Z').toISOString()
        },
        {
          id: 'comment-3',
          text: 'I\'ve been looking for something like this. Thanks for sharing!',
          userId: 'local-user3',
          userName: 'Mike Waters',
          userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-12-17T16:30:00Z').toISOString()
        }
      ],
      commentCount: 2
    },
    {
      id: 'local-1703123456793',
      title: 'Wheat Field Ready for Harvest',
      description: 'Our winter wheat is looking excellent this year. The golden color indicates it\'s ready for harvest. Expecting a great yield!',
      category: 'crops',
      fileUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      fileType: 'image/jpeg',
      userId: 'local-user3',
      userName: 'Mike Waters',
      userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-12-16T11:00:00Z').toISOString(),
      updatedAt: new Date('2024-12-16T11:00:00Z').toISOString(),
      likes: ['local-user1', 'local-user2'],
      likeCount: 2,
      comments: [],
      commentCount: 0
    }
  ];

  // Add sample users too
  const sampleUsers = [
    {
      uid: 'local-user1',
      email: '<EMAIL>',
      displayName: 'John Farmer',
      firstName: 'John',
      lastName: 'Farmer',
      farmName: 'Green Valley Farm',
      location: 'Iowa, USA',
      bio: 'Sustainable farming enthusiast with 15 years of experience',
      photoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-01-01T00:00:00Z').toISOString()
    },
    {
      uid: 'local-user2',
      email: '<EMAIL>',
      displayName: 'Sarah Green',
      firstName: 'Sarah',
      lastName: 'Green',
      farmName: 'Organic Harvest Co.',
      location: 'California, USA',
      bio: 'Organic farming specialist focusing on vegetables and herbs',
      photoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-01-15T00:00:00Z').toISOString()
    },
    {
      uid: 'local-user3',
      email: '<EMAIL>',
      displayName: 'Mike Waters',
      firstName: 'Mike',
      lastName: 'Waters',
      farmName: 'Waters Grain Farm',
      location: 'Nebraska, USA',
      bio: 'Grain farmer specializing in water conservation techniques',
      photoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      createdAt: new Date('2024-02-01T00:00:00Z').toISOString()
    }
  ];

  // Store in localStorage
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('uploads', JSON.stringify(sampleUploads));
    localStorage.setItem('users', JSON.stringify(sampleUsers));
    console.log('Sample content added to localStorage');
    return true;
  } else {
    console.log('localStorage not available');
    return false;
  }
};

// For browser usage
if (typeof window !== 'undefined') {
  window.addSampleContent = addSampleContent;
}
