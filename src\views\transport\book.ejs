<%- include('../partials/header') %>

<!-- Transport CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-3">
      <!-- Sidebar -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Transport Services
          </h5>
        </div>
        <div class="p-3">
          <div class="d-grid gap-2">
            <a href="/transport" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-house-door-fill me-2"></i> Home
            </a>
            <a href="/transport/my-bookings" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-calendar-check me-2"></i> My Bookings
            </a>
            <% if (userData && userData.isDriver) { %>
              <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-speedometer2 me-2"></i> Driver Dashboard
              </a>
            <% } else { %>
              <a href="/transport/register-driver" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-person-plus-fill me-2"></i> Register as Driver
              </a>
            <% } %>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-9">
      <!-- Main Content -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-calendar-plus me-2" style="color: var(--network-primary);"></i>
            Book Transport with <%= driver.user ? driver.user.displayName : 'Driver' %>
          </h5>
        </div>
        <div class="linkedin-card-body p-4">
          <% if (req.query.error) { %>
            <div class="alert alert-danger" role="alert">
              <%= req.query.error %>
            </div>
          <% } %>
          
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="text-center">
                <% if (driver.user && driver.user.photoURL) { %>
                  <img src="<%= driver.user.photoURL %>" alt="Driver photo" class="img-fluid rounded-circle" style="width: 120px; height: 120px; object-fit: cover; border: 3px solid var(--network-primary-light);">
                <% } else { %>
                  <div class="d-flex align-items-center justify-content-center bg-light rounded-circle mx-auto" style="width: 120px; height: 120px; border: 3px solid var(--network-primary-light);">
                    <i class="bi bi-person-fill text-secondary" style="font-size: 3rem;"></i>
                  </div>
                <% } %>
              </div>
            </div>
            <div class="col-md-9">
              <h4><%= driver.user ? driver.user.displayName : 'Driver' %></h4>
              <div class="mb-2">
                <i class="bi bi-star-fill text-warning me-1"></i>
                <span class="fw-bold"><%= driver.rating || '0.0' %></span>
                <span class="text-muted">(<%= driver.totalRatings || 0 %> ratings)</span>
              </div>
              <div class="mb-2">
                <i class="bi bi-calendar-check me-1"></i>
                <span><%= driver.experience || 0 %> years experience</span>
              </div>
              <p class="mb-0"><%= driver.bio || 'No bio available' %></p>
            </div>
          </div>
          
          <form action="/transport/book" method="POST">
            <input type="hidden" name="driverId" value="<%= driver.id %>">
            
            <div class="row">
              <div class="col-12">
                <h5 class="mb-3">Booking Details</h5>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehicleId" class="form-label">Select Vehicle</label>
                <select class="form-select" id="vehicleId" name="vehicleId" required>
                  <option value="" disabled selected>Select a vehicle</option>
                  <% vehicles.forEach(vehicle => { %>
                    <option value="<%= vehicle.id %>">
                      <%= vehicle.make %> <%= vehicle.model %> (<%= vehicle.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) %>) - <%= vehicle.capacity %>kg capacity
                    </option>
                  <% }); %>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="pickupDate" class="form-label">Pickup Date</label>
                <input type="datetime-local" class="form-control" id="pickupDate" name="pickupDate" required>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="deliveryDate" class="form-label">Estimated Delivery Date (Optional)</label>
                <input type="datetime-local" class="form-control" id="deliveryDate" name="deliveryDate">
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="pickupLocation" class="form-label">Pickup Location</label>
                <input type="text" class="form-control" id="pickupLocation" name="pickupLocation" required>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="deliveryLocation" class="form-label">Delivery Location</label>
                <input type="text" class="form-control" id="deliveryLocation" name="deliveryLocation" required>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="cargoWeight" class="form-label">Cargo Weight (kg)</label>
                <input type="number" class="form-control" id="cargoWeight" name="cargoWeight" min="0" step="0.1" required>
              </div>
              
              <div class="col-12 mb-3">
                <label for="cargoDescription" class="form-label">Cargo Description</label>
                <textarea class="form-control" id="cargoDescription" name="cargoDescription" rows="2" required placeholder="Describe what you need to transport"></textarea>
              </div>
              
              <div class="col-12 mb-4">
                <label for="specialInstructions" class="form-label">Special Instructions (Optional)</label>
                <textarea class="form-control" id="specialInstructions" name="specialInstructions" rows="3" placeholder="Any special handling instructions or requirements"></textarea>
              </div>
              
              <div class="col-12">
                <div class="d-grid gap-2">
                  <button type="submit" class="linkedin-btn linkedin-btn-primary">
                    <i class="bi bi-calendar-check me-2"></i> Confirm Booking
                  </button>
                  <a href="/transport" class="linkedin-btn linkedin-btn-outline">
                    <i class="bi bi-x-lg me-2"></i> Cancel
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>
<script>
  // Set minimum date for pickup and delivery
  document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    
    const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
    
    document.getElementById('pickupDate').min = minDateTime;
    document.getElementById('deliveryDate').min = minDateTime;
    
    // Set delivery date min based on pickup date
    document.getElementById('pickupDate').addEventListener('change', function() {
      document.getElementById('deliveryDate').min = this.value;
    });
  });
</script>

<%- include('../partials/footer') %>
