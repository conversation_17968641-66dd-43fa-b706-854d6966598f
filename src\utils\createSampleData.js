import { 
  collection, 
  doc, 
  setDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase.js';

// Sample uploads data for testing
const sampleUploads = [
  {
    id: 'upload1',
    title: 'Organic Tomato Farming',
    description: 'Learn how to grow organic tomatoes using sustainable farming practices.',
    category: 'Crops',
    fileUrl: 'https://images.unsplash.com/photo-1592841200221-a6898f307baa?w=400',
    fileType: 'image/jpeg',
    userId: 'user1',
    userName: '<PERSON>',
    userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
    createdAt: new Date('2024-01-15'),
    likes: 15,
    comments: 3,
    shares: 2
  },
  {
    id: 'upload2',
    title: 'Water Conservation Techniques',
    description: 'Effective methods for conserving water in agricultural practices.',
    category: 'Farming Techniques',
    fileUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    fileType: 'image/jpeg',
    userId: 'user2',
    userName: '<PERSON>',
    userPhotoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
    createdAt: new Date('2024-01-14'),
    likes: 23,
    comments: 7,
    shares: 5
  },
  {
    id: 'upload3',
    title: 'Composting Guide',
    description: 'Step-by-step guide to creating nutrient-rich compost for your farm.',
    category: 'Farming Techniques',
    fileUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    fileType: 'image/jpeg',
    userId: 'user3',
    userName: 'Mike Organic',
    userPhotoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
    createdAt: new Date('2024-01-13'),
    likes: 18,
    comments: 4,
    shares: 3
  },
  {
    id: 'upload4',
    title: 'Sustainable Corn Harvest',
    description: 'Our latest corn harvest using eco-friendly farming methods.',
    category: 'Crops',
    fileUrl: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=400',
    fileType: 'image/jpeg',
    userId: 'user4',
    userName: 'Lisa Harvest',
    userPhotoURL: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
    createdAt: new Date('2024-01-12'),
    likes: 31,
    comments: 9,
    shares: 7
  },
  {
    id: 'upload5',
    title: 'Greenhouse Management',
    description: 'Tips for maintaining optimal conditions in your greenhouse.',
    category: 'Farming Techniques',
    fileUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    fileType: 'image/jpeg',
    userId: 'user5',
    userName: 'David Tech',
    userPhotoURL: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',
    createdAt: new Date('2024-01-11'),
    likes: 12,
    comments: 2,
    shares: 1
  },
  {
    id: 'upload6',
    title: 'Organic Fertilizer Recipe',
    description: 'Natural fertilizer recipe that boosts crop yield sustainably.',
    category: 'Farming Techniques',
    fileUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    fileType: 'image/jpeg',
    userId: 'user1',
    userName: 'John Farmer',
    userPhotoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
    createdAt: new Date('2024-01-10'),
    likes: 27,
    comments: 6,
    shares: 4
  }
];

// Sample users data
const sampleUsers = [
  {
    uid: 'user1',
    email: '<EMAIL>',
    displayName: 'John Farmer',
    firstName: 'John',
    lastName: 'Farmer',
    location: 'Iowa, USA',
    farmName: 'Green Valley Farm',
    bio: 'Organic farming enthusiast with 10+ years of experience.',
    photoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
    isAdmin: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    uid: 'user2',
    email: '<EMAIL>',
    displayName: 'Sarah Green',
    firstName: 'Sarah',
    lastName: 'Green',
    location: 'California, USA',
    farmName: 'Sustainable Acres',
    bio: 'Passionate about water conservation and sustainable agriculture.',
    photoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
    isAdmin: false,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-14')
  },
  {
    uid: 'admin1',
    email: '<EMAIL>',
    displayName: 'Admin User',
    firstName: 'Admin',
    lastName: 'User',
    location: 'Global',
    farmName: 'Sustainable Farming Platform',
    bio: 'Platform administrator and sustainable farming advocate.',
    photoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
    isAdmin: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  }
];

// Function to create sample data
export const createSampleData = async () => {
  try {
    console.log('Creating sample data...');
    
    // Create sample uploads
    for (const upload of sampleUploads) {
      const uploadRef = doc(db, 'uploads', upload.id);
      await setDoc(uploadRef, {
        ...upload,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    // Create sample users
    for (const user of sampleUsers) {
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        ...user,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    console.log('Sample data created successfully!');
    return { success: true, message: 'Sample data created successfully!' };
  } catch (error) {
    console.error('Error creating sample data:', error);
    return { success: false, error: error.message };
  }
};

// Function to check if sample data exists
export const checkSampleData = async () => {
  try {
    const { getDocs, query, limit: limitQuery } = await import('firebase/firestore');
    
    // Check if uploads exist
    const uploadsQuery = query(collection(db, 'uploads'), limitQuery(1));
    const uploadsSnapshot = await getDocs(uploadsQuery);
    
    return !uploadsSnapshot.empty;
  } catch (error) {
    console.error('Error checking sample data:', error);
    return false;
  }
};
