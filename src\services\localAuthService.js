/**
 * Local authentication service using browser's localStorage
 * This is for development/testing purposes only and should not be used in production
 */

// Store for registered users, current user, and uploads
let users = [];
let currentUser = null;
let uploads = [];

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && window.localStorage;

// Initialize from localStorage if in browser
if (isBrowser) {
  try {
    const storedUsers = localStorage.getItem('sustainable_farming_users');
    if (storedUsers) {
      users = JSON.parse(storedUsers);
    }

    const storedUser = localStorage.getItem('sustainable_farming_current_user');
    if (storedUser) {
      currentUser = JSON.parse(storedUser);
    }

    const storedUploads = localStorage.getItem('sustainable_farming_uploads');
    if (storedUploads) {
      uploads = JSON.parse(storedUploads);
    }
  } catch (error) {
    console.error('Error initializing from localStorage:', error);
  }
}

// Save to localStorage if in browser
const saveToStorage = () => {
  if (isBrowser) {
    try {
      localStorage.setItem('sustainable_farming_users', JSON.stringify(users));

      if (currentUser) {
        localStorage.setItem('sustainable_farming_current_user', JSON.stringify(currentUser));
      } else {
        localStorage.removeItem('sustainable_farming_current_user');
      }

      localStorage.setItem('sustainable_farming_uploads', JSON.stringify(uploads));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }
};

// Register a new user
export const registerUser = (userData) => {
  // Check if email already exists
  if (users.some(user => user.email === userData.email)) {
    throw new Error('Email already in use');
  }

  // Create user object with ID and creation time
  const newUser = {
    ...userData,
    uid: Date.now().toString(),
    metadata: {
      creationTime: new Date().toISOString()
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Remove password from the object to be stored as currentUser
  const { password, ...userWithoutPassword } = newUser;

  // Add user to array
  users.push(newUser);
  saveToStorage();

  return userWithoutPassword;
};

// Login user
export const loginUser = (email, password) => {
  // Find user by email and password
  const user = users.find(user => user.email === email && user.password === password);

  if (!user) {
    throw new Error('Invalid email or password');
  }

  // Set current user (without password)
  const { password: _, ...userWithoutPassword } = user;
  currentUser = userWithoutPassword;
  saveToStorage();

  return currentUser;
};

// Get current user
export const getCurrentUser = () => {
  return currentUser;
};

// Logout user
export const logoutUser = () => {
  currentUser = null;
  saveToStorage();
};

// Check if user is authenticated
export const isAuthenticated = () => {
  return !!currentUser;
};

// Update user profile
export const updateUserProfile = (userData) => {
  if (!currentUser) {
    throw new Error('No authenticated user');
  }

  // Find and update user
  const updatedUsers = users.map(user => {
    if (user.uid === currentUser.uid) {
      const updatedUser = {
        ...user,
        ...userData,
        updatedAt: new Date().toISOString()
      };

      // Update current user
      const { password, ...userWithoutPassword } = updatedUser;
      currentUser = userWithoutPassword;

      return updatedUser;
    }
    return user;
  });

  users = updatedUsers;
  saveToStorage();

  return currentUser;
};

// Get user by ID
export const getUserById = (userId) => {
  const user = users.find(user => user.uid === userId);
  if (user) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  return null;
};

// Upload management functions
export const getUploads = () => {
  return uploads;
};

export const getUploadById = (uploadId) => {
  return uploads.find(upload => upload.id === uploadId);
};

export const getUploadsByUser = (userId) => {
  return uploads.filter(upload => upload.userId === userId);
};

export const addUpload = (uploadData) => {
  if (!currentUser) {
    throw new Error('You must be logged in to add content');
  }

  const newUpload = {
    ...uploadData,
    id: Date.now().toString(),
    userId: currentUser.uid,
    userName: currentUser.displayName,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  uploads.push(newUpload);
  saveToStorage();

  return newUpload;
};

export const deleteUpload = (uploadId) => {
  if (!currentUser) {
    throw new Error('You must be logged in to delete content');
  }

  const uploadIndex = uploads.findIndex(upload => upload.id === uploadId);

  if (uploadIndex === -1) {
    throw new Error('Content not found');
  }

  if (uploads[uploadIndex].userId !== currentUser.uid) {
    throw new Error('You can only delete your own content');
  }

  uploads.splice(uploadIndex, 1);
  saveToStorage();

  return true;
};

// Add demo user only (no pre-populated content)
if (users.length === 0) {
  try {
    // Create demo user
    registerUser({
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User',
      location: 'Test Location',
      farmName: 'Test Farm'
    });

    saveToStorage();
    console.log('Demo user created');
  } catch (error) {
    console.error('Error creating demo user:', error);
  }
}
