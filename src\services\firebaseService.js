import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc
} from 'firebase/firestore';
import {
  ref,
  uploadString,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { auth, db, storage } from '../config/initFirebase.js';

// Polyfill localStorage for Node.js environment
if (typeof localStorage === 'undefined' || localStorage === null) {
  let store = {};
  global.localStorage = {
    getItem: function(key) {
      return store[key] || null;
    },
    setItem: function(key, value) {
      store[key] = value.toString();
    },
    removeItem: function(key) {
      delete store[key];
    },
    clear: function() {
      store = {};
    }
  };
}

// Collections
const USERS_COLLECTION = 'users';
const UPLOADS_COLLECTION = 'uploads';

// User Authentication Functions

// Register a new user
export const registerUser = async (userData) => {
  try {
    // Check if Firebase Auth is properly configured
    if (!auth) {
      throw new Error('Firebase Authentication is not initialized');
    }

    // For development/testing purposes, we'll use a fallback to local storage if Firebase Auth fails
    try {
      // Try to create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      const user = userCredential.user;

      // Update profile with display name
      await updateProfile(user, {
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`
      });

      // Store additional user data in Firestore
      const userDocRef = doc(db, USERS_COLLECTION, user.uid);
      await setDoc(userDocRef, {
        email: userData.email,
        displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
        firstName: userData.firstName,
        lastName: userData.lastName,
        farmName: userData.farmName || '',
        location: userData.location || '',
        bio: userData.bio || '',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return user;
    } catch (firebaseError) {
      console.error('Firebase Auth error:', firebaseError);

      // If Firebase Auth fails with configuration error, use local storage as fallback
      if (firebaseError.code === 'auth/configuration-not-found') {
        console.log('Using local storage fallback for authentication');

        // Create a mock user object
        const mockUser = {
          uid: 'local-' + Date.now(),
          email: userData.email,
          displayName: userData.displayName || `${userData.firstName} ${userData.lastName}`,
          metadata: {
            creationTime: new Date().toISOString()
          }
        };

        // Store user in localStorage for development/testing
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        users.push({
          ...mockUser,
          password: userData.password, // WARNING: This is not secure, only for development
          firstName: userData.firstName,
          lastName: userData.lastName,
          farmName: userData.farmName || '',
          location: userData.location || '',
          bio: userData.bio || ''
        });
        localStorage.setItem('users', JSON.stringify(users));
        localStorage.setItem('currentUser', JSON.stringify(mockUser));

        return mockUser;
      } else {
        // For other errors, rethrow
        throw firebaseError;
      }
    }
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

// Login user
export const loginUser = async (email, password) => {
  try {
    // Check if Firebase Auth is properly configured
    if (!auth) {
      throw new Error('Firebase Authentication is not initialized');
    }

    try {
      // Try to sign in with Firebase Auth
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (firebaseError) {
      console.error('Firebase Auth error:', firebaseError);

      // If Firebase Auth fails with configuration error, use local storage as fallback
      if (firebaseError.code === 'auth/configuration-not-found') {
        console.log('Using local storage fallback for authentication');

        // Check if user exists in localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.email === email && u.password === password);

        if (!user) {
          throw new Error('Invalid email or password');
        }

        // Create a mock user object without the password
        const mockUser = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          metadata: user.metadata
        };

        // Store current user in localStorage
        localStorage.setItem('currentUser', JSON.stringify(mockUser));

        return mockUser;
      } else {
        // For other errors, rethrow
        throw firebaseError;
      }
    }
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

// Logout user
export const logoutUser = async () => {
  try {
    await signOut(auth);
    return true;
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};

// Get current user
export const getCurrentUser = () => {
  // Try to get user from Firebase Auth
  const firebaseUser = auth.currentUser;

  if (firebaseUser) {
    return firebaseUser;
  }

  // Fallback to localStorage if Firebase Auth is not configured
  try {
    const localUser = JSON.parse(localStorage.getItem('currentUser'));
    return localUser;
  } catch (error) {
    console.error('Error getting current user from localStorage:', error);
    return null;
  }
};

// Check if user is authenticated
export const isAuthenticated = () => {
  // Try Firebase Auth first
  if (auth.currentUser) {
    return true;
  }

  // Fallback to localStorage
  try {
    const localUser = JSON.parse(localStorage.getItem('currentUser'));
    return !!localUser;
  } catch (error) {
    console.error('Error checking authentication status from localStorage:', error);
    return false;
  }
};

// Get all users
export const getUsers = async () => {
  try {
    // Try to get users from Firestore
    try {
      const usersQuery = query(collection(db, USERS_COLLECTION));
      const usersSnapshot = await getDocs(usersQuery);

      return usersSnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      }));
    } catch (firestoreError) {
      console.error('Firestore error getting users:', firestoreError);

      // Fallback to localStorage
      try {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        return users.map(user => {
          // Remove sensitive data
          const { password, ...userWithoutPassword } = user;
          return userWithoutPassword;
        });
      } catch (localStorageError) {
        console.error('Error getting users from localStorage:', localStorageError);
        return [];
      }
    }
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
};

// Get user data from Firestore
export const getUserData = async (userId) => {
  try {
    // Try to get user data from Firestore
    try {
      const userDocRef = doc(db, USERS_COLLECTION, userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        return userDoc.data();
      }
    } catch (firestoreError) {
      console.error('Firestore error:', firestoreError);

      // If Firestore fails, check if this is a local user
      if (userId && userId.startsWith('local-')) {
        console.log('Using localStorage fallback for user data');
      } else {
        // For other errors with Firebase users, rethrow
        throw firestoreError;
      }
    }

    // Fallback to localStorage
    try {
      const users = JSON.parse(localStorage.getItem('users') || '[]');
      const user = users.find(u => u.uid === userId);

      if (user) {
        // Return user data without sensitive information
        const { password, ...userData } = user;
        return userData;
      }
    } catch (localStorageError) {
      console.error('Error getting user data from localStorage:', localStorageError);
    }

    return null;
  } catch (error) {
    console.error('Error getting user data:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (userData) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    // Check if this is a Firebase user or local user
    if (user.uid && !user.uid.startsWith('local-')) {
      try {
        // Update display name if provided
        if (userData.displayName) {
          await updateProfile(user, {
            displayName: userData.displayName
          });
        }

        // Update user data in Firestore
        const userDocRef = doc(db, USERS_COLLECTION, user.uid);
        await updateDoc(userDocRef, {
          ...userData,
          updatedAt: serverTimestamp()
        });
      } catch (firebaseError) {
        console.error('Firebase error updating profile:', firebaseError);

        // If Firebase fails with configuration error, use localStorage fallback
        if (firebaseError.code && (
            firebaseError.code === 'auth/configuration-not-found' ||
            firebaseError.code.includes('firestore')
        )) {
          console.log('Using localStorage fallback for profile update');
        } else {
          throw firebaseError;
        }
      }
    }

    // Fallback to localStorage or update local user
    try {
      const users = JSON.parse(localStorage.getItem('users') || '[]');
      const userIndex = users.findIndex(u => u.uid === user.uid);

      if (userIndex !== -1) {
        // Update user data
        users[userIndex] = {
          ...users[userIndex],
          ...userData,
          displayName: userData.displayName || users[userIndex].displayName,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('users', JSON.stringify(users));

        // Update current user if display name changed
        if (userData.displayName) {
          const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
          currentUser.displayName = userData.displayName;
          localStorage.setItem('currentUser', JSON.stringify(currentUser));
        }
      }
    } catch (localStorageError) {
      console.error('Error updating user in localStorage:', localStorageError);
    }

    return user;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Change user password
export const changeUserPassword = async (currentPassword, newPassword) => {
  try {
    const user = auth.currentUser;

    if (!user) {
      throw new Error('No authenticated user');
    }

    // Re-authenticate user before changing password
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // Update password
    await updatePassword(user, newPassword);

    return true;
  } catch (error) {
    console.error('Error changing password:', error);
    throw error;
  }
};

// Update profile picture
export const updateProfilePicture = async (userId, photoDataUrl) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    if (user.uid !== userId) {
      throw new Error('You can only update your own profile picture');
    }

    let photoURL = photoDataUrl;

    // Try to use Firebase Storage
    try {
      // Generate a unique filename based on timestamp
      const timestamp = Date.now();
      const filename = `profile_${timestamp}.jpg`;

      // Create a reference to the file in Firebase Storage
      const fileRef = ref(storage, `profile_pictures/${userId}/${filename}`);

      // Upload the file to Firebase Storage
      await uploadString(fileRef, photoDataUrl, 'data_url');

      // Get the download URL
      photoURL = await getDownloadURL(fileRef);

      // Update Firebase Auth profile
      await updateProfile(user, { photoURL });

      // Update user data in Firestore
      const userDocRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userDocRef, {
        photoURL,
        updatedAt: serverTimestamp()
      });
    } catch (firebaseError) {
      console.error('Firebase error updating profile picture:', firebaseError);

      // If Firebase fails, use localStorage fallback
      console.log('Using localStorage fallback for profile picture update');

      // For local storage, we'll just use the data URL directly
      // Update Firebase Auth profile if possible
      try {
        await updateProfile(user, { photoURL });
      } catch (authError) {
        console.error('Error updating auth profile:', authError);
      }
    }

    // Fallback to localStorage
    try {
      const users = JSON.parse(localStorage.getItem('users') || '[]');
      const userIndex = users.findIndex(u => u.uid === userId);

      if (userIndex !== -1) {
        // Update user data
        users[userIndex] = {
          ...users[userIndex],
          photoURL,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('users', JSON.stringify(users));

        // Update current user
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        currentUser.photoURL = photoURL;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
      }
    } catch (localStorageError) {
      console.error('Error updating user in localStorage:', localStorageError);
    }

    return photoURL;
  } catch (error) {
    console.error('Error updating profile picture:', error);
    throw error;
  }
};

// Content Upload Functions

// Get all uploads with pagination support
export const getUploads = async (options = {}) => {
  try {
    const {
      page = 1,
      limit: limitCount = 10,
      filter = 'all',
      skip = 0
    } = options;

    const uploads = [];
    let total = 0;
    let hasMore = false;

    // Try to get uploads from Firestore
    try {
      // Build query based on filter
      let uploadsQuery = collection(db, UPLOADS_COLLECTION);

      // Apply category filter if not 'all'
      if (filter !== 'all') {
        uploadsQuery = query(uploadsQuery, where('category', '==', filter));
      }

      // Add ordering and pagination
      uploadsQuery = query(
        uploadsQuery,
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1) // Get one extra to check if there are more
      );

      const querySnapshot = await getDocs(uploadsQuery);

      // Get total count for pagination info
      const countQuery = filter !== 'all'
        ? query(collection(db, UPLOADS_COLLECTION), where('category', '==', filter))
        : collection(db, UPLOADS_COLLECTION);

      const countSnapshot = await getDocs(countQuery);
      total = countSnapshot.size;

      // Process results
      const docs = querySnapshot.docs;
      hasMore = docs.length > limitCount;

      // Remove the extra document if we have more
      if (hasMore) {
        docs.pop();
      }

      docs.forEach((doc) => {
        const data = doc.data();
        uploads.push({
          id: doc.id,
          ...data,
          // Convert Firestore timestamps to JavaScript dates
          createdAt: data.createdAt?.toDate?.() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.() || data.updatedAt
        });
      });

      // If we got uploads from Firestore, return them
      if (uploads.length > 0 || page > 1) {
        return {
          uploads,
          total,
          hasMore,
          page,
          limit: limitCount
        };
      }
    } catch (firestoreError) {
      console.error('Firestore error getting uploads:', firestoreError);
      console.log('Using localStorage fallback for uploads');
    }

    // Fallback to localStorage
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');

      // Apply filter
      let filteredUploads = localUploads;
      if (filter !== 'all') {
        filteredUploads = localUploads.filter(upload => upload.category === filter);
      }

      // Sort by creation date (newest first)
      filteredUploads.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // Apply pagination
      const startIndex = (page - 1) * limitCount;
      const endIndex = startIndex + limitCount;
      const paginatedUploads = filteredUploads.slice(startIndex, endIndex);

      total = filteredUploads.length;
      hasMore = endIndex < total;

      return {
        uploads: paginatedUploads,
        total,
        hasMore,
        page,
        limit: limitCount
      };
    } catch (localStorageError) {
      console.error('Error getting uploads from localStorage:', localStorageError);
      return {
        uploads: [],
        total: 0,
        hasMore: false,
        page,
        limit: limitCount
      };
    }
  } catch (error) {
    console.error('Error getting uploads:', error);
    return {
      uploads: [],
      total: 0,
      hasMore: false,
      page,
      limit: limitCount
    };
  }
};

// Get upload by ID
export const getUploadById = async (uploadId) => {
  try {
    // Try to get upload from Firestore
    try {
      const uploadDocRef = doc(db, UPLOADS_COLLECTION, uploadId);
      const uploadDoc = await getDoc(uploadDocRef);

      if (uploadDoc.exists()) {
        return {
          id: uploadDoc.id,
          ...uploadDoc.data()
        };
      }
    } catch (firestoreError) {
      console.error('Firestore error getting upload:', firestoreError);
      console.log('Using localStorage fallback for upload');
    }

    // Fallback to localStorage
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      const upload = localUploads.find(u => u.id === uploadId);

      if (upload) {
        return upload;
      }
    } catch (localStorageError) {
      console.error('Error getting upload from localStorage:', localStorageError);
    }

    return null;
  } catch (error) {
    console.error('Error getting upload:', error);
    return null;
  }
};

// Get uploads by user
export const getUploadsByUser = async (userId) => {
  try {
    const uploads = [];

    // Try to get uploads from Firestore
    try {
      const uploadsQuery = query(
        collection(db, UPLOADS_COLLECTION),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(uploadsQuery);

      querySnapshot.forEach((doc) => {
        uploads.push({
          id: doc.id,
          ...doc.data()
        });
      });

      // If we got uploads from Firestore, return them
      return uploads;
    } catch (firestoreError) {
      console.error('Firestore error getting user uploads:', firestoreError);

      // Check if this is an indexing error (which is expected during development)
      if (firestoreError.message && firestoreError.message.includes('requires an index')) {
        console.log('Index required for this query. Using localStorage fallback for user uploads');
      } else {
        // For other errors, log and continue to fallback
        console.log('Using localStorage fallback for user uploads');
      }
    }

    // Fallback to localStorage
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      return localUploads.filter(upload => upload.userId === userId);
    } catch (localStorageError) {
      console.error('Error getting user uploads from localStorage:', localStorageError);
      return [];
    }
  } catch (error) {
    console.error('Error getting user uploads:', error);
    return []; // Return empty array instead of throwing
  }
};

// Add new upload
export const addUpload = async (uploadData) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to add content');
    }

    let fileUrl = uploadData.fileUrl;
    let uploadId = null;
    let fileType = '';

    // Determine file type from data URL
    if (fileUrl && fileUrl.startsWith('data:')) {
      const mimeTypeMatch = fileUrl.match(/^data:([^;]+);/);
      if (mimeTypeMatch) {
        fileType = mimeTypeMatch[1];
        console.log('Detected file type:', fileType);
      }
    }

    // Generate a unique filename based on timestamp and file type
    const timestamp = Date.now();
    const fileExtension = getFileExtensionFromMimeType(fileType);
    const filename = `${timestamp}${fileExtension ? '.' + fileExtension : ''}`;

    console.log('Uploading file with name:', filename);

    // Try to use Firebase
    try {
      // If the fileUrl is a data URL, upload it to Firebase Storage
      if (fileUrl && fileUrl.startsWith('data:')) {
        try {
          console.log('Uploading to Firebase Storage...');

          // Create a reference to the file in Firebase Storage
          const fileRef = ref(storage, `uploads/${user.uid}/${filename}`);

          // Upload the file to Firebase Storage
          await uploadString(fileRef, fileUrl, 'data_url');
          console.log('File uploaded to Firebase Storage');

          // Get the download URL
          fileUrl = await getDownloadURL(fileRef);
          console.log('Download URL obtained:', fileUrl);
        } catch (storageError) {
          console.error('Firebase Storage error:', storageError);
          console.log('Using data URL as fallback');
          // Keep the original data URL if Firebase Storage fails
        }
      }

      console.log('Adding document to Firestore...');

      // Get user data to include profile picture
      let userPhotoURL = null;
      try {
        const userData = await getUserData(user.uid);
        if (userData && userData.photoURL) {
          userPhotoURL = userData.photoURL;
        }
      } catch (userDataError) {
        console.error('Error getting user data for upload:', userDataError);
        // Continue without the photo URL
      }

      // Add upload to Firestore with likes and comments arrays
      const uploadRef = await addDoc(collection(db, UPLOADS_COLLECTION), {
        title: uploadData.title,
        description: uploadData.description,
        category: uploadData.category,
        fileUrl: fileUrl,
        fileType: fileType,
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userPhotoURL: userPhotoURL,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        likes: [],
        likeCount: 0,
        comments: [],
        commentCount: 0
      });

      uploadId = uploadRef.id;
      console.log('Document added to Firestore with ID:', uploadId);

      // Get the new upload with ID
      const newUpload = await getUploadById(uploadId);

      if (newUpload) {
        return newUpload;
      }
    } catch (firebaseError) {
      console.error('Firebase error adding upload:', firebaseError);
      console.log('Using localStorage fallback for upload');
    }

    // Fallback to localStorage
    try {
      console.log('Using localStorage fallback...');

      // Get user data for profile picture from localStorage
      let userPhotoURL = null;
      try {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userData = users.find(u => u.uid === user.uid);
        if (userData && userData.photoURL) {
          userPhotoURL = userData.photoURL;
        }
      } catch (error) {
        console.error('Error getting user photo from localStorage:', error);
      }

      // Create a new upload object with likes and comments
      const newUpload = {
        id: 'local-' + timestamp,
        title: uploadData.title,
        description: uploadData.description,
        category: uploadData.category,
        fileUrl: fileUrl,
        fileType: fileType,
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userPhotoURL: userPhotoURL,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        likes: [],
        likeCount: 0,
        comments: [],
        commentCount: 0
      };

      // Add to localStorage
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      localUploads.push(newUpload);
      localStorage.setItem('uploads', JSON.stringify(localUploads));

      console.log('Upload added to localStorage with ID:', newUpload.id);

      return newUpload;
    } catch (localStorageError) {
      console.error('Error adding upload to localStorage:', localStorageError);
      throw new Error('Failed to add content. Please try again.');
    }
  } catch (error) {
    console.error('Error adding upload:', error);
    throw error;
  }
};

// Helper function to get file extension from MIME type
function getFileExtensionFromMimeType(mimeType) {
  const mimeToExt = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'image/svg+xml': 'svg',
    'video/mp4': 'mp4',
    'video/webm': 'webm',
    'video/ogg': 'ogg',
    'application/pdf': 'pdf',
    'text/plain': 'txt',
    'application/json': 'json'
  };

  return mimeToExt[mimeType] || '';
}

// Like an upload
export const likeUpload = async (uploadId, user = null) => {
  try {
    // Get current user (from parameter or getCurrentUser for client-side)
    const currentUser = user || getCurrentUser();
    console.log('Current user in likeUpload:', currentUser ? currentUser.uid : 'No user');

    if (!currentUser) {
      throw new Error('You must be logged in to like content');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);
    console.log('Upload found:', upload ? 'Yes' : 'No');

    if (!upload) {
      throw new Error('Content not found');
    }

    // Check if this is a Firebase upload or local upload
    if (!uploadId.startsWith('local-')) {
      try {
        // Get the upload document reference
        const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

        // Check if user already liked this upload
        const likes = upload.likes || [];
        const userLiked = likes.includes(currentUser.uid);
        console.log('User already liked this upload:', userLiked);
        console.log('Current likes:', likes);

        if (userLiked) {
          // Unlike: Remove user from likes array
          const newLikes = likes.filter(uid => uid !== currentUser.uid);
          const newLikeCount = Math.max(0, (upload.likeCount || likes.length) - 1);

          console.log('Unliking upload. New likes:', newLikes);
          console.log('New like count:', newLikeCount);

          await updateDoc(uploadRef, {
            likes: newLikes,
            likeCount: newLikeCount,
            updatedAt: serverTimestamp()
          });
          console.log('Upload unliked successfully');
        } else {
          // Like: Add user to likes array
          const newLikes = [...likes, currentUser.uid];
          const newLikeCount = (upload.likeCount || likes.length) + 1;

          console.log('Liking upload. New likes:', newLikes);
          console.log('New like count:', newLikeCount);

          await updateDoc(uploadRef, {
            likes: newLikes,
            likeCount: newLikeCount,
            updatedAt: serverTimestamp()
          });
          console.log('Upload liked successfully');
        }

        // Return the updated upload
        const updatedUpload = await getUploadById(uploadId);
        console.log('Updated upload retrieved:', updatedUpload ? 'Yes' : 'No');
        return updatedUpload;
      } catch (firebaseError) {
        console.error('Firebase error liking upload:', firebaseError);
        console.log('Using localStorage fallback for like');

        // If Firebase fails with a non-critical error, continue to localStorage fallback
        if (!firebaseError.code || (
            firebaseError.code !== 'auth/configuration-not-found' &&
            !firebaseError.code.includes('firestore')
        )) {
          throw firebaseError;
        }
      }
    }

    // Fallback to localStorage
    try {
      console.log('Using localStorage fallback for like operation');
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      const uploadIndex = localUploads.findIndex(u => u.id === uploadId);
      console.log('Upload index in localStorage:', uploadIndex);

      if (uploadIndex !== -1) {
        const upload = localUploads[uploadIndex];
        const likes = upload.likes || [];
        const userLiked = likes.includes(currentUser.uid);
        console.log('User already liked this upload in localStorage:', userLiked);

        if (userLiked) {
          // Unlike: Remove user from likes array
          upload.likes = likes.filter(uid => uid !== currentUser.uid);
          upload.likeCount = Math.max(0, (upload.likeCount || likes.length) - 1);
          console.log('Unliking upload in localStorage. New like count:', upload.likeCount);
        } else {
          // Like: Add user to likes array
          upload.likes = [...likes, currentUser.uid];
          upload.likeCount = (upload.likeCount || likes.length) + 1;
          console.log('Liking upload in localStorage. New like count:', upload.likeCount);
        }

        upload.updatedAt = new Date().toISOString();
        localUploads[uploadIndex] = upload;
        localStorage.setItem('uploads', JSON.stringify(localUploads));
        console.log('Upload updated in localStorage');

        return upload;
      } else {
        console.log('Upload not found in localStorage');
      }
    } catch (localStorageError) {
      console.error('Error liking upload in localStorage:', localStorageError);
    }

    throw new Error('Failed to like content. Please try again.');
  } catch (error) {
    console.error('Error liking upload:', error);
    throw error;
  }
};

// Add a comment to an upload
export const addComment = async (uploadId, commentText, user = null) => {
  try {
    // Get current user (from parameter or getCurrentUser for client-side)
    const currentUser = user || getCurrentUser();

    if (!currentUser) {
      throw new Error('You must be logged in to comment');
    }

    if (!commentText || commentText.trim() === '') {
      throw new Error('Comment cannot be empty');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    // Get user profile picture if available
    let userPhotoURL = null;
    try {
      const userData = await getUserData(currentUser.uid);
      if (userData && userData.photoURL) {
        userPhotoURL = userData.photoURL;
      }
    } catch (error) {
      console.error('Error getting user photo for comment:', error);
      // Continue without the photo URL
    }

    // Create comment object
    const comment = {
      id: Date.now().toString(),
      text: commentText.trim(),
      userId: currentUser.uid,
      userName: currentUser.displayName || 'Anonymous',
      userPhotoURL: userPhotoURL || currentUser.photoURL || null,
      createdAt: new Date().toISOString()
    };

    // Check if this is a Firebase upload or local upload
    if (!uploadId.startsWith('local-')) {
      try {
        // Get the upload document reference
        const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

        // Add comment to comments array
        const comments = upload.comments || [];

        await updateDoc(uploadRef, {
          comments: [...comments, {
            ...comment,
            createdAt: serverTimestamp()
          }],
          commentCount: (upload.commentCount || comments.length) + 1,
          updatedAt: serverTimestamp()
        });

        console.log('Comment added');

        // Return the updated upload
        return await getUploadById(uploadId);
      } catch (firebaseError) {
        console.error('Firebase error adding comment:', firebaseError);
        console.log('Using localStorage fallback for comment');

        // If Firebase fails with a non-critical error, continue to localStorage fallback
        if (!firebaseError.code || (
            firebaseError.code !== 'auth/configuration-not-found' &&
            !firebaseError.code.includes('firestore')
        )) {
          throw firebaseError;
        }
      }
    }

    // Fallback to localStorage
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      const uploadIndex = localUploads.findIndex(u => u.id === uploadId);

      if (uploadIndex !== -1) {
        const upload = localUploads[uploadIndex];
        const comments = upload.comments || [];

        upload.comments = [...comments, comment];
        upload.commentCount = (upload.commentCount || comments.length) + 1;
        upload.updatedAt = new Date().toISOString();

        localUploads[uploadIndex] = upload;
        localStorage.setItem('uploads', JSON.stringify(localUploads));

        return upload;
      }
    } catch (localStorageError) {
      console.error('Error adding comment in localStorage:', localStorageError);
    }

    throw new Error('Failed to add comment. Please try again.');
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

// Delete a comment from an upload
export const deleteComment = async (uploadId, commentId) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to delete a comment');
    }

    // Get the upload
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    // Check if this is a Firebase upload or local upload
    if (!uploadId.startsWith('local-')) {
      try {
        // Get the upload document reference
        const uploadRef = doc(db, UPLOADS_COLLECTION, uploadId);

        // Find the comment
        const comments = upload.comments || [];
        const comment = comments.find(c => c.id === commentId);

        if (!comment) {
          throw new Error('Comment not found');
        }

        // Check if user is the comment author or the upload owner
        if (comment.userId !== user.uid && upload.userId !== user.uid) {
          throw new Error('You can only delete your own comments');
        }

        // Remove comment from comments array
        const updatedComments = comments.filter(c => c.id !== commentId);

        await updateDoc(uploadRef, {
          comments: updatedComments,
          commentCount: (upload.commentCount || comments.length) - 1,
          updatedAt: serverTimestamp()
        });

        console.log('Comment deleted');

        // Return the updated upload
        return await getUploadById(uploadId);
      } catch (firebaseError) {
        console.error('Firebase error deleting comment:', firebaseError);
        console.log('Using localStorage fallback for delete comment');

        // If Firebase fails with a non-critical error, continue to localStorage fallback
        if (!firebaseError.code || (
            firebaseError.code !== 'auth/configuration-not-found' &&
            !firebaseError.code.includes('firestore')
        )) {
          throw firebaseError;
        }
      }
    }

    // Fallback to localStorage
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      const uploadIndex = localUploads.findIndex(u => u.id === uploadId);

      if (uploadIndex !== -1) {
        const upload = localUploads[uploadIndex];
        const comments = upload.comments || [];
        const comment = comments.find(c => c.id === commentId);

        if (!comment) {
          throw new Error('Comment not found');
        }

        // Check if user is the comment author or the upload owner
        if (comment.userId !== user.uid && upload.userId !== user.uid) {
          throw new Error('You can only delete your own comments');
        }

        // Remove comment from comments array
        upload.comments = comments.filter(c => c.id !== commentId);
        upload.commentCount = (upload.commentCount || comments.length) - 1;
        upload.updatedAt = new Date().toISOString();

        localUploads[uploadIndex] = upload;
        localStorage.setItem('uploads', JSON.stringify(localUploads));

        return upload;
      }
    } catch (localStorageError) {
      console.error('Error deleting comment in localStorage:', localStorageError);
    }

    throw new Error('Failed to delete comment. Please try again.');
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

// Delete upload
export const deleteUpload = async (uploadId) => {
  try {
    // Get current user
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to delete content');
    }

    // Get the upload to check ownership and get the file URL
    const upload = await getUploadById(uploadId);

    if (!upload) {
      throw new Error('Content not found');
    }

    if (upload.userId !== user.uid) {
      throw new Error('You can only delete your own content');
    }

    // Check if this is a Firebase upload or local upload
    if (!uploadId.startsWith('local-')) {
      try {
        // If the file is stored in Firebase Storage, delete it
        if (upload.fileUrl && upload.fileUrl.includes('firebasestorage.googleapis.com')) {
          try {
            const fileRef = ref(storage, upload.fileUrl);
            await deleteObject(fileRef);
          } catch (storageError) {
            console.error('Error deleting file from storage:', storageError);
            // Continue with deleting the document even if file deletion fails
          }
        }

        // Delete the upload document from Firestore
        await deleteDoc(doc(db, UPLOADS_COLLECTION, uploadId));
      } catch (firebaseError) {
        console.error('Firebase error deleting upload:', firebaseError);
        console.log('Using localStorage fallback for delete');

        // If Firebase fails with a non-critical error, continue to localStorage fallback
        if (!firebaseError.code || (
            firebaseError.code !== 'auth/configuration-not-found' &&
            !firebaseError.code.includes('firestore')
        )) {
          throw firebaseError;
        }
      }
    }

    // Fallback to localStorage or delete local upload
    try {
      const localUploads = JSON.parse(localStorage.getItem('uploads') || '[]');
      const updatedUploads = localUploads.filter(u => u.id !== uploadId);

      if (localUploads.length !== updatedUploads.length) {
        localStorage.setItem('uploads', JSON.stringify(updatedUploads));
      }
    } catch (localStorageError) {
      console.error('Error deleting upload from localStorage:', localStorageError);
    }

    return true;
  } catch (error) {
    console.error('Error deleting upload:', error);
    throw error;
  }
};
