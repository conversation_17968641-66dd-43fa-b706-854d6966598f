import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import * as transportService from '../services/transportService.js';

const router = express.Router();

// Main transport page
router.get('/', async (req, res) => {
  // Check if user is authenticated
  if (!req.user) {
    return res.redirect('/auth/login?message=Please log in to access transport services');
  }
  try {
    // Get available drivers
    const drivers = await transportService.getAvailableDrivers();

    res.render('transport/index', {
      user: req.user,
      userData: req.userData,
      title: 'Transport Services',
      drivers: drivers
    });
  } catch (error) {
    console.error('Error loading transport page:', error);

    // Check if this is a permission error
    if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
      res.render('transport/index', {
        user: req.user,
        userData: req.userData,
        title: 'Transport Services',
        drivers: [],
        indexError: true,
        errorMessage: 'Database permissions are being configured. Please try again in a few minutes or contact support.'
      });
    } else {
      // For other errors, render the transport page with empty data instead of error page
      res.render('transport/index', {
        user: req.user,
        userData: req.userData,
        title: 'Transport Services',
        drivers: [],
        indexError: true,
        errorMessage: 'Unable to load transport services at the moment. Please try again later.'
      });
    }
  }
});

// Driver registration page
router.get('/register-driver', isAuthenticated, async (req, res) => {
  try {
    res.render('transport/register-driver', {
      user: req.user,
      userData: req.userData,
      title: 'Register as Driver',
      vehicleTypes: transportService.VEHICLE_TYPES
    });
  } catch (error) {
    console.error('Error loading driver registration page:', error);
    res.render('error', {
      error: 'Error loading driver registration',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Process driver registration
router.post('/register-driver', isAuthenticated, async (req, res) => {
  try {
    const {
      licenseNumber,
      licenseExpiry,
      experience,
      vehicleType,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehiclePlate,
      vehicleCapacity,
      bio
    } = req.body;

    // Register driver
    const driverResult = await transportService.registerDriver({
      licenseNumber,
      licenseExpiry,
      experience: parseInt(experience) || 0,
      bio
    });

    // Add vehicle
    if (driverResult.success) {
      await transportService.addVehicle({
        type: vehicleType,
        make: vehicleMake,
        model: vehicleModel,
        year: parseInt(vehicleYear) || new Date().getFullYear(),
        licensePlate: vehiclePlate,
        capacity: vehicleCapacity
      });
    }

    res.redirect('/transport/driver-dashboard');
  } catch (error) {
    console.error('Error registering driver:', error);
    res.render('transport/register-driver', {
      user: req.user,
      userData: req.userData,
      title: 'Register as Driver',
      vehicleTypes: transportService.VEHICLE_TYPES,
      error: error.message,
      formData: req.body
    });
  }
});

// Driver dashboard
router.get('/driver-dashboard', isAuthenticated, async (req, res) => {
  try {
    // Get driver bookings
    const bookings = await transportService.getDriverBookings();

    // Get driver vehicles
    const vehicles = await transportService.getDriverVehicles(req.user.uid);

    res.render('transport/driver-dashboard', {
      user: req.user,
      userData: req.userData,
      title: 'Driver Dashboard',
      bookings: bookings,
      vehicles: vehicles,
      bookingStatus: transportService.BOOKING_STATUS
    });
  } catch (error) {
    console.error('Error loading driver dashboard:', error);
    res.render('error', {
      error: 'Error loading driver dashboard',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Add vehicle page
router.get('/add-vehicle', isAuthenticated, async (req, res) => {
  try {
    res.render('transport/add-vehicle', {
      user: req.user,
      userData: req.userData,
      title: 'Add Vehicle',
      vehicleTypes: transportService.VEHICLE_TYPES
    });
  } catch (error) {
    console.error('Error loading add vehicle page:', error);
    res.render('error', {
      error: 'Error loading add vehicle page',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Process add vehicle
router.post('/add-vehicle', isAuthenticated, async (req, res) => {
  try {
    const {
      vehicleType,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehiclePlate,
      vehicleCapacity
    } = req.body;

    // Add vehicle
    await transportService.addVehicle({
      type: vehicleType,
      make: vehicleMake,
      model: vehicleModel,
      year: parseInt(vehicleYear) || new Date().getFullYear(),
      licensePlate: vehiclePlate,
      capacity: vehicleCapacity
    });

    res.redirect('/transport/driver-dashboard');
  } catch (error) {
    console.error('Error adding vehicle:', error);
    res.render('transport/add-vehicle', {
      user: req.user,
      userData: req.userData,
      title: 'Add Vehicle',
      vehicleTypes: transportService.VEHICLE_TYPES,
      error: error.message,
      formData: req.body
    });
  }
});

// Book transport page
router.get('/book/:driverId', isAuthenticated, async (req, res) => {
  try {
    const { driverId } = req.params;

    // Get driver data
    const drivers = await transportService.getAvailableDrivers();
    const driver = drivers.find(d => d.id === driverId);

    if (!driver) {
      throw new Error('Driver not found');
    }

    // Get driver vehicles
    const vehicles = await transportService.getDriverVehicles(driverId);

    res.render('transport/book', {
      user: req.user,
      userData: req.userData,
      title: 'Book Transport',
      driver: driver,
      vehicles: vehicles
    });
  } catch (error) {
    console.error('Error loading booking page:', error);
    res.render('error', {
      error: 'Error loading booking page',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Process booking
router.post('/book', isAuthenticated, async (req, res) => {
  try {
    const {
      driverId,
      vehicleId,
      pickupDate,
      deliveryDate,
      pickupLocation,
      deliveryLocation,
      cargoDescription,
      cargoWeight,
      specialInstructions
    } = req.body;

    // Create booking
    await transportService.createBooking({
      driverId,
      vehicleId,
      pickupDate,
      deliveryDate,
      pickupLocation,
      deliveryLocation,
      cargoDescription,
      cargoWeight: parseFloat(cargoWeight) || 0,
      specialInstructions
    });

    res.redirect('/transport/my-bookings');
  } catch (error) {
    console.error('Error creating booking:', error);
    res.redirect(`/transport/book/${req.body.driverId}?error=${encodeURIComponent(error.message)}`);
  }
});

// My bookings page
router.get('/my-bookings', isAuthenticated, async (req, res) => {
  try {
    // Get user bookings
    const bookings = await transportService.getUserBookings();

    res.render('transport/my-bookings', {
      user: req.user,
      userData: req.userData,
      title: 'My Bookings',
      bookings: bookings,
      bookingStatus: transportService.BOOKING_STATUS
    });
  } catch (error) {
    console.error('Error loading my bookings page:', error);
    res.render('error', {
      error: 'Error loading bookings',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// API endpoints for transport functionality

// Get available drivers
router.get('/api/drivers', isAuthenticated, async (req, res) => {
  try {
    const drivers = await transportService.getAvailableDrivers();
    res.json({ success: true, drivers });
  } catch (error) {
    console.error('Error getting drivers:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get driver vehicles
router.get('/api/vehicles/:driverId', isAuthenticated, async (req, res) => {
  try {
    const { driverId } = req.params;
    const vehicles = await transportService.getDriverVehicles(driverId);
    res.json({ success: true, vehicles });
  } catch (error) {
    console.error('Error getting vehicles:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

export default router;
