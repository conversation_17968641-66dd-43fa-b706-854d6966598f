// Enhanced Weather functionality for Sustainable Farming application

document.addEventListener('DOMContentLoaded', function() {
  // DOM elements
  const citySearchInput = document.getElementById('citySearch');
  const searchCityBtn = document.getElementById('searchCityBtn');
  const getCurrentLocationBtn = document.getElementById('getCurrentLocationBtn');
  const currentWeatherContainer = document.getElementById('currentWeatherContainer');
  const forecastContainer = document.getElementById('forecastContainer');
  const weatherMapContainer = document.getElementById('weatherMapContainer');
  const farmingWeatherContainer = document.getElementById('farmingWeatherContainer');
  const weatherErrorAlert = document.getElementById('weatherErrorAlert');
  const weatherErrorMessage = document.getElementById('weatherErrorMessage');
  const forecastRow = document.getElementById('forecastRow');
  const farmingConditionsList = document.getElementById('farmingConditionsList');
  const farmingRecommendationsList = document.getElementById('farmingRecommendationsList');
  const loadingIndicator = document.createElement('div');
  const airQualityContainer = document.getElementById('airQualityContainer');
  const weatherAlertsContainer = document.getElementById('weatherAlertsContainer');
  const hourlyForecastContainer = document.getElementById('hourlyForecastContainer');
  const hourlyForecastRow = document.getElementById('hourlyForecastRow');
  const precipitationContainer = document.getElementById('precipitationContainer');
  const alertsList = document.getElementById('alertsList');
  const unitToggle = document.getElementById('unitToggle');
  const refreshWeatherBtn = document.getElementById('refreshWeatherBtn');
  const quickCityButtons = document.querySelectorAll('.weather-map-btn[data-city]');
  const weatherMapButtons = document.querySelectorAll('.weather-map-btn[data-map]');
  const lastUpdated = document.getElementById('lastUpdated');

  // Weather elements
  const locationName = document.getElementById('locationName');
  const locationCountry = document.getElementById('locationCountry');
  const currentDate = document.getElementById('currentDate');
  const currentTemp = document.getElementById('currentTemp');
  const weatherDescription = document.getElementById('weatherDescription');
  const feelsLike = document.getElementById('feelsLike');
  const humidity = document.getElementById('humidity');
  const windSpeed = document.getElementById('windSpeed');
  const sunrise = document.getElementById('sunrise');
  const sunset = document.getElementById('sunset');
  const weatherIcon = document.getElementById('weatherIcon');
  const pressure = document.getElementById('pressure');
  const visibility = document.getElementById('visibility');
  const uvIndex = document.getElementById('uvIndex');
  const cloudiness = document.getElementById('cloudiness');

  // Air quality elements
  const aqiGauge = document.getElementById('aqiGauge');
  const pm25 = document.getElementById('pm25');
  const pm10 = document.getElementById('pm10');
  const o3 = document.getElementById('o3');
  const no2 = document.getElementById('no2');

  // Precipitation chart
  let precipitationChart = null;

  // Create and add loading indicator
  loadingIndicator.className = 'weather-loading-indicator';
  loadingIndicator.innerHTML = `
    <div class="spinner-border" style="color: var(--theme-primary, #4CAF50); width: 3rem; height: 3rem;" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3 fs-5 text-white">Loading weather data...</p>
  `;
  loadingIndicator.style.display = 'none';
  document.querySelector('.container').appendChild(loadingIndicator);

  // Weather map functionality
  let weatherMap = null;
  let weatherMapLayer = null;
  const weatherMapApiKey = 'e4f215c88a9a9713e4f3b74bd3942da0';
  let currentMapType = 'temp';
  let currentMapCoordinates = { lat: 0, lon: 0 };

  // Temperature unit (default: Celsius)
  let useImperial = false;

  // Event listeners
  searchCityBtn.addEventListener('click', searchWeatherByCity);
  getCurrentLocationBtn.addEventListener('click', getWeatherByCurrentLocation);
  citySearchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchWeatherByCity();
    }
  });

  // Quick city buttons
  quickCityButtons.forEach(button => {
    button.addEventListener('click', function() {
      const city = this.dataset.city;
      citySearchInput.value = city;
      searchWeatherByCity();
    });
  });

  // Weather map type buttons
  weatherMapButtons.forEach(button => {
    button.addEventListener('click', function() {
      const mapType = this.dataset.map;

      // Update active button
      weatherMapButtons.forEach(btn => btn.classList.remove('active'));
      this.classList.add('active');

      // Update map layer
      currentMapType = mapType;
      updateWeatherMap();
    });
  });

  // Unit toggle
  unitToggle.addEventListener('change', function() {
    useImperial = this.checked;
    updateTemperatureDisplay();
  });

  // Refresh button
  refreshWeatherBtn.addEventListener('click', function() {
    const savedLocation = JSON.parse(localStorage.getItem('lastWeatherLocation'));
    if (savedLocation && savedLocation.lat && savedLocation.lon) {
      getWeatherByCoordinates(savedLocation.lat, savedLocation.lon);
    } else {
      getWeatherByCurrentLocation();
    }
  });

  // Initialize weather map
  function initializeWeatherMap(lat, lon) {
    if (!weatherMap) {
      const mapElement = document.getElementById('weatherMap');

      // Create OpenLayers map
      try {
        // Create a simple placeholder for the weather map
        mapElement.innerHTML = `
          <div style="position: relative; width: 100%; height: 100%;">
            <img src="https://tile.openweathermap.org/map/${currentMapType}/1/${lat}/${lon}.png?appid=${weatherMapApiKey}"
                 style="width: 100%; height: 100%; object-fit: cover;" alt="Weather Map">
            <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(255,255,255,0.7); padding: 5px; border-radius: 5px; font-size: 12px;">
              © OpenWeatherMap
            </div>
          </div>
        `;

        // Show the weather map container
        weatherMapContainer.style.display = 'block';
      } catch (error) {
        console.error('Error initializing weather map:', error);
      }
    } else {
      updateWeatherMap();
    }
  }

  // Update weather map
  function updateWeatherMap() {
    const mapElement = document.getElementById('weatherMap');
    const { lat, lon } = currentMapCoordinates;

    // Update the map image
    mapElement.innerHTML = `
      <div style="position: relative; width: 100%; height: 100%;">
        <img src="https://tile.openweathermap.org/map/${currentMapType}/1/${lat}/${lon}.png?appid=${weatherMapApiKey}"
             style="width: 100%; height: 100%; object-fit: cover;" alt="Weather Map">
        <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(255,255,255,0.7); padding: 5px; border-radius: 5px; font-size: 12px;">
          © OpenWeatherMap
        </div>
      </div>
    `;
  }

  // Check for saved location or try to get current location on page load
  initializeWeather();

  // Search weather by city name
  async function searchWeatherByCity() {
    const city = citySearchInput.value.trim();

    if (!city) {
      showError('Please enter a city name');
      return;
    }

    try {
      hideError();
      showLoading();

      // Fetch comprehensive weather data
      const oneCallResponse = await fetchCityOneCallData(city);

      // Fetch air quality data
      const airQualityResponse = await fetchCityAirQuality(city);

      // Display all weather data
      displayComprehensiveWeatherData(oneCallResponse, airQualityResponse);

    } catch (error) {
      console.error('Error fetching weather data:', error);
      showError(error.message || 'Failed to fetch weather data. Please try again.');
    } finally {
      hideLoading();
    }
  }

  // Get weather by current location
  function getWeatherByCurrentLocation() {
    if (!navigator.geolocation) {
      showError('Geolocation is not supported by your browser');
      return;
    }

    hideError();
    showLoading();

    // Add loading indicator to the button
    const originalButtonText = getCurrentLocationBtn.innerHTML;
    getCurrentLocationBtn.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> Getting location...';
    getCurrentLocationBtn.disabled = true;

    // Options for geolocation
    const options = {
      enableHighAccuracy: true,  // Get high accuracy if available
      timeout: 10000,            // Time to wait before error (10 seconds)
      maximumAge: 0              // Don't use cached position
    };

    navigator.geolocation.getCurrentPosition(
      async position => {
        try {
          const { latitude, longitude } = position.coords;

          console.log(`Location obtained: Lat ${latitude}, Lon ${longitude}`);

          // Store location in localStorage for future use
          localStorage.setItem('lastWeatherLocation', JSON.stringify({
            lat: latitude,
            lon: longitude,
            timestamp: new Date().getTime()
          }));

          // Get weather by coordinates
          await getWeatherByCoordinates(latitude, longitude);

        } catch (error) {
          console.error('Error fetching weather data:', error);
          showError(error.message || 'Failed to fetch weather data. Please try again.');
        } finally {
          hideLoading();
          getCurrentLocationBtn.innerHTML = originalButtonText;
          getCurrentLocationBtn.disabled = false;
        }
      },
      error => {
        hideLoading();
        getCurrentLocationBtn.innerHTML = originalButtonText;
        getCurrentLocationBtn.disabled = false;

        let errorMessage = 'Failed to get your location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'You denied the request for geolocation. Please enable location services in your browser settings.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable. Please try again or search by city name.';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get your location timed out. Please try again or search by city name.';
            break;
        }

        console.error('Geolocation error:', error);
        showError(errorMessage);
      },
      options
    );
  }

  // Get weather by coordinates
  async function getWeatherByCoordinates(latitude, longitude) {
    try {
      hideError();
      showLoading();

      // Fetch comprehensive weather data
      const oneCallResponse = await fetchOneCallData(latitude, longitude);

      // Fetch air quality data
      const airQualityResponse = await fetchAirQuality(latitude, longitude);

      // Display all weather data
      displayComprehensiveWeatherData(oneCallResponse, airQualityResponse);

    } catch (error) {
      console.error('Error fetching weather data:', error);
      showError(error.message || 'Failed to fetch weather data. Please try again.');
    } finally {
      hideLoading();
    }
  }

  // Fetch One Call API data
  async function fetchOneCallData(lat, lon) {
    const response = await fetch(`/weather/api/onecall?lat=${lat}&lon=${lon}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch comprehensive weather data');
    }

    return await response.json();
  }

  // Fetch city-based One Call API data
  async function fetchCityOneCallData(city) {
    // First get coordinates for the city
    const currentWeatherResponse = await fetch(`/weather/api/city/current?city=${encodeURIComponent(city)}`);

    if (!currentWeatherResponse.ok) {
      const errorData = await currentWeatherResponse.json();
      throw new Error(errorData.error || 'Failed to fetch city coordinates');
    }

    const currentWeatherData = await currentWeatherResponse.json();

    // Store location in localStorage
    localStorage.setItem('lastWeatherLocation', JSON.stringify({
      lat: currentWeatherData.lat,
      lon: currentWeatherData.lon,
      timestamp: new Date().getTime()
    }));

    // Then get One Call data using the coordinates
    return await fetchOneCallData(currentWeatherData.lat, currentWeatherData.lon);
  }

  // Fetch air quality data
  async function fetchAirQuality(lat, lon) {
    const response = await fetch(`/weather/api/air-quality?lat=${lat}&lon=${lon}`);

    if (!response.ok) {
      console.warn('Air quality data not available');
      return null;
    }

    return await response.json();
  }

  // Fetch city-based air quality data
  async function fetchCityAirQuality(city) {
    // First get coordinates for the city
    const currentWeatherResponse = await fetch(`/weather/api/city/current?city=${encodeURIComponent(city)}`);

    if (!currentWeatherResponse.ok) {
      console.warn('Could not get city coordinates for air quality');
      return null;
    }

    const currentWeatherData = await currentWeatherResponse.json();

    // Then get air quality data using the coordinates
    return await fetchAirQuality(currentWeatherData.lat, currentWeatherData.lon);
  }

  // Display comprehensive weather data
  function displayComprehensiveWeatherData(oneCallData, airQualityData) {
    if (!oneCallData) {
      showError('No weather data available');
      return;
    }

    // Display current weather
    const current = oneCallData.current;
    locationName.textContent = oneCallData.timezone.split('/').pop().replace('_', ' ');
    locationCountry.textContent = oneCallData.timezone.split('/')[0];
    currentDate.textContent = new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Set temperature based on unit preference
    updateTemperatureValue(currentTemp, current.temperature);
    weatherDescription.textContent = current.description;
    updateTemperatureValue(feelsLike, current.feelsLike);
    humidity.textContent = `${current.humidity}%`;
    windSpeed.textContent = `${current.windSpeed} m/s`;

    // Set weather icon
    weatherIcon.src = `https://openweathermap.org/img/wn/${current.icon}@2x.png`;
    weatherIcon.alt = current.description;

    // Set additional weather details
    pressure.textContent = `${current.pressure} hPa`;
    visibility.textContent = `${(current.visibility / 1000).toFixed(1)} km`;
    uvIndex.textContent = current.uvIndex;
    cloudiness.textContent = `${current.cloudiness || 0}%`;

    // Update last updated time
    lastUpdated.textContent = new Date().toLocaleTimeString();

    // Show current weather container
    currentWeatherContainer.style.display = 'block';

    // Display forecast
    displayEnhancedForecast(oneCallData.daily);

    // Display hourly forecast
    displayHourlyForecast(oneCallData.hourly);

    // Display precipitation chart
    displayPrecipitationChart(oneCallData.hourly);

    // Display air quality if available
    if (airQualityData) {
      displayAirQuality(airQualityData);
    } else {
      airQualityContainer.style.display = 'none';
    }

    // Display weather alerts if available
    if (oneCallData.alerts && oneCallData.alerts.length > 0) {
      displayWeatherAlerts(oneCallData.alerts);
    } else {
      weatherAlertsContainer.style.display = 'none';
    }

    // Display farming recommendations
    displayEnhancedFarmingRecommendations(current, oneCallData.daily);

    // Initialize weather map
    currentMapCoordinates = { lat: oneCallData.lat, lon: oneCallData.lon };
    initializeWeatherMap(oneCallData.lat, oneCallData.lon);
  }

  // Display enhanced forecast data
  function displayEnhancedForecast(forecastData) {
    if (!forecastData || forecastData.length === 0) {
      forecastContainer.style.display = 'none';
      return;
    }

    // Clear previous forecast
    forecastRow.innerHTML = '';

    // Add forecast cards (limit to 5 days)
    forecastData.slice(0, 5).forEach(day => {
      const dayOfWeek = new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' });
      const shortDate = new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      const forecastCard = document.createElement('div');
      forecastCard.className = 'col-lg-2 col-md-4 col-6 mb-3';

      // Format temperatures based on unit preference
      const tempDay = useImperial ? celsiusToFahrenheit(day.tempDay) + '°F' : day.tempDay + '°C';
      const tempMin = useImperial ? celsiusToFahrenheit(day.tempMin) + '°F' : day.tempMin + '°C';
      const tempMax = useImperial ? celsiusToFahrenheit(day.tempMax) + '°F' : day.tempMax + '°C';

      forecastCard.innerHTML = `
        <div class="forecast-card text-center">
          <h5 class="forecast-day">${dayOfWeek}</h5>
          <p class="forecast-date">${shortDate}</p>
          <img src="https://openweathermap.org/img/wn/${day.icon}@2x.png" alt="${day.description}" class="weather-icon mb-2">
          <p class="forecast-temp">${tempDay}</p>
          <div class="forecast-min-max">
            <span class="forecast-max">${tempMax}</span>
            <span>/</span>
            <span class="forecast-min">${tempMin}</span>
          </div>
          <p class="forecast-description">${day.description}</p>
          <div class="forecast-details">
            <div><i class="bi bi-droplet-fill me-1" style="color: var(--weather-info);"></i> ${day.pop}% chance</div>
            <div><i class="bi bi-wind me-1" style="color: var(--weather-secondary);"></i> ${day.windSpeed} m/s</div>
            <div><i class="bi bi-sun me-1" style="color: var(--weather-warning);"></i> UV: ${day.uvIndex || 'N/A'}</div>
          </div>
        </div>
      `;

      forecastRow.appendChild(forecastCard);
    });

    // Show forecast container
    forecastContainer.style.display = 'block';
  }

  // Display hourly forecast
  function displayHourlyForecast(hourlyData) {
    if (!hourlyData || hourlyData.length === 0) {
      hourlyForecastContainer.style.display = 'none';
      return;
    }

    // Clear previous hourly forecast
    hourlyForecastRow.innerHTML = '';

    // Add hourly forecast items (next 24 hours)
    hourlyData.slice(0, 24).forEach(hour => {
      const hourlyItem = document.createElement('div');
      hourlyItem.className = 'hourly-forecast-item';

      // Format temperature based on unit preference
      const temp = useImperial ? celsiusToFahrenheit(hour.temperature) + '°F' : hour.temperature + '°C';

      // Format time to be more readable
      const hourTime = new Date(hour.timestamp).toLocaleTimeString('en-US', {
        hour: 'numeric',
        hour12: true
      });

      hourlyItem.innerHTML = `
        <div class="hourly-forecast-time">${hourTime}</div>
        <img src="https://openweathermap.org/img/wn/${hour.icon}.png" alt="${hour.description}" class="weather-icon-small">
        <div class="hourly-forecast-temp">${temp}</div>
        <div class="hourly-forecast-pop"><i class="bi bi-droplet-fill"></i> ${hour.pop}%</div>
      `;

      hourlyForecastRow.appendChild(hourlyItem);
    });

    // Show hourly forecast container
    hourlyForecastContainer.style.display = 'block';
  }

  // Display precipitation chart
  function displayPrecipitationChart(hourlyData) {
    if (!hourlyData || hourlyData.length === 0) {
      precipitationContainer.style.display = 'none';
      return;
    }

    // Prepare data for chart
    const labels = hourlyData.slice(0, 24).map(hour => {
      return new Date(hour.timestamp).toLocaleTimeString('en-US', {
        hour: 'numeric',
        hour12: true
      });
    });

    const popData = hourlyData.slice(0, 24).map(hour => hour.pop);
    const tempData = hourlyData.slice(0, 24).map(hour => hour.temperature);

    // Get canvas context
    const ctx = document.getElementById('precipitationChart').getContext('2d');

    // Destroy previous chart if it exists
    if (precipitationChart) {
      precipitationChart.destroy();
    }

    // Create gradient for precipitation bars
    const precipGradient = ctx.createLinearGradient(0, 0, 0, 400);
    precipGradient.addColorStop(0, 'rgba(33, 150, 243, 0.8)');
    precipGradient.addColorStop(1, 'rgba(33, 150, 243, 0.2)');

    // Create gradient for temperature line
    const tempGradient = ctx.createLinearGradient(0, 0, 0, 400);
    tempGradient.addColorStop(0, 'rgba(255, 87, 34, 0.8)');
    tempGradient.addColorStop(1, 'rgba(255, 87, 34, 0.2)');

    // Create new chart
    precipitationChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Precipitation Probability (%)',
            data: popData,
            backgroundColor: precipGradient,
            borderColor: 'rgba(33, 150, 243, 1)',
            borderWidth: 1,
            borderRadius: 4,
            yAxisID: 'y'
          },
          {
            label: 'Temperature (°C)',
            data: tempData,
            type: 'line',
            borderColor: 'rgba(255, 87, 34, 1)',
            backgroundColor: tempGradient,
            borderWidth: 2,
            pointBackgroundColor: 'rgba(255, 87, 34, 1)',
            pointBorderColor: '#fff',
            pointRadius: 4,
            pointHoverRadius: 6,
            fill: false,
            tension: 0.4,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            titleColor: '#333',
            bodyColor: '#666',
            borderColor: '#ddd',
            borderWidth: 1,
            padding: 12,
            boxPadding: 6,
            usePointStyle: true,
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.datasetIndex === 0) {
                  label += context.parsed.y + '%';
                } else {
                  label += useImperial ? celsiusToFahrenheit(context.parsed.y) + '°F' : context.parsed.y + '°C';
                }
                return label;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'Probability (%)',
              color: 'rgba(33, 150, 243, 1)',
              font: {
                weight: 'bold'
              }
            },
            grid: {
              display: true,
              drawBorder: true,
              color: 'rgba(200, 200, 200, 0.2)'
            },
            ticks: {
              color: 'rgba(33, 150, 243, 1)'
            }
          },
          y1: {
            position: 'right',
            title: {
              display: true,
              text: useImperial ? 'Temperature (°F)' : 'Temperature (°C)',
              color: 'rgba(255, 87, 34, 1)',
              font: {
                weight: 'bold'
              }
            },
            grid: {
              display: false
            },
            ticks: {
              color: 'rgba(255, 87, 34, 1)',
              callback: function(value) {
                return useImperial ? celsiusToFahrenheit(value) + '°F' : value + '°C';
              }
            }
          },
          x: {
            grid: {
              display: false
            },
            title: {
              display: true,
              text: 'Time',
              font: {
                weight: 'bold'
              }
            }
          }
        }
      }
    });

    // Show precipitation container
    precipitationContainer.style.display = 'block';
  }

  // Display air quality data
  function displayAirQuality(airQualityData) {
    if (!airQualityData) {
      airQualityContainer.style.display = 'none';
      return;
    }

    // Update AQI gauge
    aqiGauge.style.backgroundColor = 'white';
    aqiGauge.querySelector('.aqi-value').textContent = airQualityData.aqi;
    aqiGauge.querySelector('.aqi-label').textContent = airQualityData.label;

    // Set color based on AQI level
    aqiGauge.querySelector('.aqi-value').style.color = airQualityData.color;
    aqiGauge.querySelector('.aqi-label').style.color = airQualityData.color;

    // Update component values
    pm25.textContent = `${airQualityData.components.pm2_5} μg/m³`;
    pm10.textContent = `${airQualityData.components.pm10} μg/m³`;
    o3.textContent = `${airQualityData.components.o3} μg/m³`;
    no2.textContent = `${airQualityData.components.no2} μg/m³`;

    // Add health recommendations based on AQI
    let healthRecommendation = '';
    switch(airQualityData.aqi) {
      case 1:
        healthRecommendation = 'Air quality is good. Ideal for outdoor activities and farming work.';
        break;
      case 2:
        healthRecommendation = 'Air quality is fair. Most people can perform outdoor activities normally.';
        break;
      case 3:
        healthRecommendation = 'Air quality is moderate. Sensitive individuals should consider limiting prolonged outdoor exertion.';
        break;
      case 4:
        healthRecommendation = 'Air quality is poor. Consider reducing prolonged or heavy outdoor exertion.';
        break;
      case 5:
        healthRecommendation = 'Air quality is very poor. Avoid prolonged or heavy outdoor exertion.';
        break;
      default:
        healthRecommendation = 'No air quality data available.';
    }

    // Add health recommendation to the page
    const infoCard = document.querySelector('#airQualityContainer .card-body .card');
    if (infoCard) {
      const infoText = infoCard.querySelector('p');
      if (infoText) {
        infoText.innerHTML = `
          Air quality data is provided by OpenWeatherMap. The Air Quality Index (AQI) ranges from 1 (Good) to 5 (Very Poor).<br>
          <strong>Health Recommendation:</strong> ${healthRecommendation}
        `;
      }
    }

    // Show air quality container
    airQualityContainer.style.display = 'block';
  }

  // Display weather alerts
  function displayWeatherAlerts(alertsData) {
    if (!alertsData || alertsData.length === 0) {
      weatherAlertsContainer.style.display = 'none';
      return;
    }

    // Clear previous alerts
    alertsList.innerHTML = '';

    // Add alerts
    alertsData.forEach(alert => {
      const alertElement = document.createElement('div');
      alertElement.className = 'weather-alert';

      // Format dates for better readability
      const startDate = new Date(alert.start);
      const endDate = new Date(alert.end);

      const formattedStart = startDate.toLocaleString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });

      const formattedEnd = endDate.toLocaleString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });

      // Determine alert severity icon
      let severityIcon = 'bi-exclamation-triangle-fill';
      let severityClass = 'text-warning';

      if (alert.event.toLowerCase().includes('warning')) {
        severityIcon = 'bi-exclamation-octagon-fill';
        severityClass = 'text-danger';
      } else if (alert.event.toLowerCase().includes('watch')) {
        severityIcon = 'bi-eye-fill';
        severityClass = 'text-warning';
      } else if (alert.event.toLowerCase().includes('advisory')) {
        severityIcon = 'bi-info-circle-fill';
        severityClass = 'text-info';
      }

      alertElement.innerHTML = `
        <div class="weather-alert-title">
          <i class="bi ${severityIcon} ${severityClass} me-2"></i>
          ${alert.event}
        </div>
        <div class="weather-alert-time">
          <i class="bi bi-clock me-2"></i>
          From: ${formattedStart} - To: ${formattedEnd}
        </div>
        <div class="weather-alert-description">${alert.description}</div>
        <div class="mt-2">
          <small class="text-muted">Issued by: ${alert.senderName || 'Weather Service'}</small>
        </div>
      `;

      alertsList.appendChild(alertElement);
    });

    // Show alerts container
    weatherAlertsContainer.style.display = 'block';
  }

  // Display enhanced farming recommendations
  function displayEnhancedFarmingRecommendations(currentWeather, forecastData) {
    if (!currentWeather) return;

    // Clear previous recommendations
    farmingConditionsList.innerHTML = '';
    farmingRecommendationsList.innerHTML = '';

    // Current conditions analysis
    const conditions = [];
    const recommendations = [];

    // Temperature analysis
    if (currentWeather.temperature < 5) {
      conditions.push({
        text: 'Cold temperatures may affect plant growth and development',
        type: 'unfavorable'
      });
      recommendations.push('Consider using cold frames or row covers to protect sensitive crops');
      recommendations.push('Delay planting of warm-season crops until temperatures rise');
    } else if (currentWeather.temperature > 30) {
      conditions.push({
        text: 'High temperatures may cause heat stress in plants',
        type: 'unfavorable'
      });
      recommendations.push('Increase watering frequency and consider shade cloth for sensitive crops');
      recommendations.push('Water in early morning or evening to reduce evaporation');
    } else if (currentWeather.temperature > 20 && currentWeather.temperature <= 30) {
      conditions.push({
        text: 'Ideal temperature range for most warm-season crops',
        type: 'favorable'
      });
      recommendations.push('Good conditions for planting warm-season crops like tomatoes, peppers, and cucumbers');
    } else {
      conditions.push({
        text: 'Temperature is suitable for cool-season crops',
        type: 'favorable'
      });
      recommendations.push('Good conditions for crops like lettuce, spinach, and peas');
    }

    // Humidity analysis
    if (currentWeather.humidity < 30) {
      conditions.push({
        text: 'Low humidity may increase water loss through evaporation',
        type: 'unfavorable'
      });
      recommendations.push('Consider mulching to retain soil moisture');
      recommendations.push('Increase irrigation frequency but with smaller amounts');
    } else if (currentWeather.humidity > 80) {
      conditions.push({
        text: 'High humidity may increase risk of fungal diseases',
        type: 'unfavorable'
      });
      recommendations.push('Ensure good air circulation around plants and avoid overhead watering');
      recommendations.push('Monitor for signs of fungal diseases and apply preventative treatments if necessary');
    } else {
      conditions.push({
        text: 'Humidity is within optimal range for most crops',
        type: 'favorable'
      });
    }

    // UV Index analysis
    if (currentWeather.uvIndex > 7) {
      conditions.push({
        text: 'High UV index may cause sunburn on sensitive crops and workers',
        type: 'unfavorable'
      });
      recommendations.push('Consider providing shade for sensitive crops');
      recommendations.push('Farm workers should use sun protection');
    }

    // Wind analysis
    if (currentWeather.windSpeed > 8) {
      conditions.push({
        text: 'Strong winds may damage plants and increase water loss',
        type: 'unfavorable'
      });
      recommendations.push('Consider windbreaks or temporary shelters for sensitive crops');
      recommendations.push('Delay spraying operations until wind speeds decrease');
    } else {
      conditions.push({
        text: 'Wind speed is within acceptable range for most crops',
        type: 'favorable'
      });
    }

    // Weather description analysis
    const weatherDesc = currentWeather.description.toLowerCase();

    if (weatherDesc.includes('rain') || weatherDesc.includes('drizzle')) {
      conditions.push({
        text: 'Rainfall provides natural irrigation but may increase disease risk',
        type: 'neutral'
      });
      recommendations.push('Hold off on irrigation and avoid working in wet fields to prevent soil compaction');
      recommendations.push('Monitor for signs of water-related diseases in susceptible crops');
    } else if (weatherDesc.includes('snow')) {
      conditions.push({
        text: 'Snow provides insulation but limits field access',
        type: 'neutral'
      });
      recommendations.push('Focus on indoor activities like planning and equipment maintenance');
      recommendations.push('Snow cover can protect winter crops, but check for damage after thawing');
    } else if (weatherDesc.includes('clear') || weatherDesc.includes('sun')) {
      conditions.push({
        text: 'Sunny conditions promote photosynthesis and plant growth',
        type: 'favorable'
      });
      recommendations.push('Good day for planting, harvesting, or other field activities');
      recommendations.push('Ensure adequate irrigation if conditions have been dry');
    } else if (weatherDesc.includes('cloud')) {
      conditions.push({
        text: 'Cloudy conditions reduce water loss but limit photosynthesis',
        type: 'neutral'
      });
      recommendations.push('Good conditions for transplanting seedlings');
    }

    // Forecast-based recommendations
    if (forecastData && forecastData.length > 0) {
      // Check for rain in the forecast
      const rainInForecast = forecastData.some(day =>
        day.description.toLowerCase().includes('rain') && day.pop > 50
      );

      if (rainInForecast) {
        recommendations.push('Rain expected in the coming days - plan field operations accordingly');
        recommendations.push('Consider delaying fertilizer application to prevent runoff');
      }

      // Check for temperature trends
      const tempTrend = forecastData.map(day => day.tempDay);
      const isWarming = tempTrend.every((temp, i, arr) => i === 0 || temp >= arr[i - 1]);
      const isCooling = tempTrend.every((temp, i, arr) => i === 0 || temp <= arr[i - 1]);

      if (isWarming) {
        recommendations.push('Temperatures are trending warmer - adjust irrigation accordingly');
        if (tempTrend[tempTrend.length - 1] > 30) {
          recommendations.push('Heat stress may become an issue - consider protective measures');
        }
      } else if (isCooling) {
        recommendations.push('Temperatures are trending cooler - protect sensitive crops if needed');
        if (tempTrend[tempTrend.length - 1] < 5) {
          recommendations.push('Frost protection may be necessary for sensitive crops');
        }
      }
    }

    // Add crop-specific recommendations based on season
    const currentMonth = new Date().getMonth(); // 0-11

    // Spring (March-May)
    if (currentMonth >= 2 && currentMonth <= 4) {
      recommendations.push('Spring is a good time for planting cool-season crops and preparing soil for warm-season crops');
      recommendations.push('Monitor for early-season pests like aphids and cutworms');
    }
    // Summer (June-August)
    else if (currentMonth >= 5 && currentMonth <= 7) {
      recommendations.push('Summer is peak growing season - maintain regular irrigation and pest monitoring');
      recommendations.push('Consider succession planting for continuous harvests');
    }
    // Fall (September-November)
    else if (currentMonth >= 8 && currentMonth <= 10) {
      recommendations.push('Fall is ideal for planting cool-season crops and cover crops');
      recommendations.push('Prepare for frost protection as temperatures begin to drop');
    }
    // Winter (December-February)
    else {
      recommendations.push('Winter is good for planning, soil testing, and equipment maintenance');
      recommendations.push('Consider season extension techniques like high tunnels for winter production');
    }

    // Display conditions
    conditions.forEach(condition => {
      const conditionItem = document.createElement('div');
      conditionItem.className = `farming-condition condition-${condition.type} mb-3`;

      // Add appropriate icon based on condition type
      let conditionIcon = '';
      if (condition.type === 'favorable') {
        conditionIcon = 'bi-check-circle-fill';
      } else if (condition.type === 'unfavorable') {
        conditionIcon = 'bi-x-circle-fill';
      } else {
        conditionIcon = 'bi-exclamation-circle-fill';
      }

      conditionItem.innerHTML = `
        <div class="farming-condition-title">
          <i class="bi ${conditionIcon} farming-condition-icon"></i>
          ${condition.text}
        </div>
      `;

      farmingConditionsList.appendChild(conditionItem);
    });

    // Display recommendations
    recommendations.forEach(recommendation => {
      const recommendationItem = document.createElement('div');
      recommendationItem.className = 'farming-recommendation mb-3';
      recommendationItem.innerHTML = `
        <div class="farming-recommendation-title">
          <i class="bi bi-lightbulb-fill farming-recommendation-icon"></i>
          ${recommendation}
        </div>
      `;
      farmingRecommendationsList.appendChild(recommendationItem);
    });

    // Show farming weather container
    farmingWeatherContainer.style.display = 'block';
  }

  // Initialize weather on page load
  async function initializeWeather() {
    // Check if we have a saved location that's less than 30 minutes old
    const savedLocation = JSON.parse(localStorage.getItem('lastWeatherLocation'));
    const thirtyMinutesInMs = 30 * 60 * 1000;

    if (savedLocation && (new Date().getTime() - savedLocation.timestamp < thirtyMinutesInMs)) {
      // Use saved location if it's recent
      try {
        hideError();
        showLoading();

        console.log('Using saved location:', savedLocation);

        // Get weather by coordinates
        await getWeatherByCoordinates(savedLocation.lat, savedLocation.lon);
      } catch (error) {
        console.error('Error using saved location:', error);
        // If there's an error, try to get current location
        getWeatherByCurrentLocation();
      } finally {
        hideLoading();
      }
    } else {
      // No recent saved location, try to get current location
      getWeatherByCurrentLocation();
    }
  }

  // Update temperature display based on selected unit
  function updateTemperatureDisplay() {
    // Update all temperature displays on the page
    const currentTempValue = parseFloat(currentTemp.textContent);
    const feelsLikeValue = parseFloat(feelsLike.textContent);

    if (!isNaN(currentTempValue)) {
      updateTemperatureValue(currentTemp, currentTempValue);
    }

    if (!isNaN(feelsLikeValue)) {
      updateTemperatureValue(feelsLike, feelsLikeValue);
    }

    // Update forecast temperatures
    if (forecastContainer.style.display !== 'none') {
      const forecastTemps = document.querySelectorAll('.forecast-temp');
      const forecastMinMax = document.querySelectorAll('.forecast-min-max');

      forecastTemps.forEach(el => {
        const tempValue = parseFloat(el.textContent);
        if (!isNaN(tempValue)) {
          el.textContent = useImperial ? `${celsiusToFahrenheit(tempValue)}°F` : `${tempValue}°C`;
        }
      });

      forecastMinMax.forEach(el => {
        const temps = el.textContent.split(' / ');
        const minTemp = parseFloat(temps[0]);
        const maxTemp = parseFloat(temps[1]);

        if (!isNaN(minTemp) && !isNaN(maxTemp)) {
          el.textContent = useImperial ?
            `${celsiusToFahrenheit(minTemp)}°F / ${celsiusToFahrenheit(maxTemp)}°F` :
            `${minTemp}°C / ${maxTemp}°C`;
        }
      });
    }

    // Update hourly forecast temperatures
    if (hourlyForecastContainer.style.display !== 'none') {
      const hourlyTemps = document.querySelectorAll('.hourly-forecast-temp');

      hourlyTemps.forEach(el => {
        const tempValue = parseFloat(el.textContent);
        if (!isNaN(tempValue)) {
          el.textContent = useImperial ? `${celsiusToFahrenheit(tempValue)}°F` : `${tempValue}°C`;
        }
      });
    }
  }

  // Update temperature value with proper unit
  function updateTemperatureValue(element, value) {
    if (element && !isNaN(value)) {
      element.textContent = useImperial ? `${celsiusToFahrenheit(value)}°F` : `${value}°C`;
    }
  }

  // Convert Celsius to Fahrenheit
  function celsiusToFahrenheit(celsius) {
    return Math.round((celsius * 9/5) + 32);
  }

  // Show loading state
  function showLoading() {
    loadingIndicator.style.display = 'flex';
  }

  // Hide loading state
  function hideLoading() {
    loadingIndicator.style.display = 'none';
  }

  // Show error message
  function showError(message) {
    weatherErrorMessage.textContent = message;
    weatherErrorAlert.style.display = 'block';

    // Hide containers
    currentWeatherContainer.style.display = 'none';
    forecastContainer.style.display = 'none';
    airQualityContainer.style.display = 'none';
    weatherAlertsContainer.style.display = 'none';
    hourlyForecastContainer.style.display = 'none';
    precipitationContainer.style.display = 'none';
    farmingWeatherContainer.style.display = 'none';

    // Scroll to error message
    weatherErrorAlert.scrollIntoView({ behavior: 'smooth' });
  }

  // Hide error message
  function hideError() {
    weatherErrorAlert.style.display = 'none';
  }
});
