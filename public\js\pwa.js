// PWA Registration and Management
class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.init();
    }

    async init() {
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup install prompt
        this.setupInstallPrompt();
        
        // Setup push notifications
        this.setupPushNotifications();
        
        // Setup offline detection
        this.setupOfflineDetection();
        
        // Setup background sync
        this.setupBackgroundSync();
        
        // Check if app is installed
        this.checkInstallStatus();
    }

    // Register Service Worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('PWA: Service Worker registered successfully', registration);
                
                // Handle updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateNotification();
                        }
                    });
                });
                
                return registration;
            } catch (error) {
                console.error('PWA: Service Worker registration failed', error);
            }
        } else {
            console.log('PWA: Service Worker not supported');
        }
    }

    // Setup Install Prompt
    setupInstallPrompt() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstalledNotification();
        });
    }

    // Show install button
    showInstallButton() {
        const installButton = this.createInstallButton();
        document.body.appendChild(installButton);
    }

    // Create install button
    createInstallButton() {
        const button = document.createElement('button');
        button.id = 'pwa-install-button';
        button.innerHTML = '📱 Install App';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #8BC34A;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(139, 195, 74, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        `;

        button.addEventListener('click', () => this.installApp());
        
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 6px 16px rgba(139, 195, 74, 0.4)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = '0 4px 12px rgba(139, 195, 74, 0.3)';
        });

        return button;
    }

    // Install app
    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted install prompt');
            } else {
                console.log('PWA: User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
        }
    }

    // Hide install button
    hideInstallButton() {
        const button = document.getElementById('pwa-install-button');
        if (button) {
            button.remove();
        }
    }

    // Setup Push Notifications
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            // Request notification permission
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                console.log('PWA: Notification permission granted');
                await this.subscribeToPushNotifications();
            } else {
                console.log('PWA: Notification permission denied');
            }
        }
    }

    // Subscribe to push notifications
    async subscribeToPushNotifications() {
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY') // Replace with your VAPID key
            });
            
            console.log('PWA: Push subscription successful', subscription);
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
        } catch (error) {
            console.error('PWA: Push subscription failed', error);
        }
    }

    // Convert VAPID key
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // Send subscription to server
    async sendSubscriptionToServer(subscription) {
        try {
            await fetch('/api/push-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscription)
            });
        } catch (error) {
            console.error('PWA: Failed to send subscription to server', error);
        }
    }

    // Setup Offline Detection
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            console.log('PWA: Back online');
            this.showConnectionStatus('online');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            console.log('PWA: Gone offline');
            this.showConnectionStatus('offline');
        });
    }

    // Show connection status
    showConnectionStatus(status) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transition: all 0.3s ease;
        `;

        if (status === 'online') {
            notification.style.background = '#4CAF50';
            notification.textContent = '✅ Back online - Syncing data...';
        } else {
            notification.style.background = '#F44336';
            notification.textContent = '❌ You\'re offline - Some features limited';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Setup Background Sync
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            console.log('PWA: Background sync supported');
        } else {
            console.log('PWA: Background sync not supported');
        }
    }

    // Sync offline data
    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.sync.register('background-sync');
                console.log('PWA: Background sync registered');
            } catch (error) {
                console.error('PWA: Background sync registration failed', error);
            }
        }
    }

    // Check install status
    checkInstallStatus() {
        // Check if app is running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
            this.isInstalled = true;
            console.log('PWA: App is running in standalone mode');
        }
    }

    // Show update notification
    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #2196F3;
            color: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            max-width: 300px;
        `;

        notification.innerHTML = `
            <div style="margin-bottom: 8px; font-weight: 600;">Update Available</div>
            <div style="margin-bottom: 12px; font-size: 14px;">A new version of the app is available.</div>
            <button onclick="window.location.reload()" style="background: white; color: #2196F3; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: 600;">Update Now</button>
            <button onclick="this.parentElement.remove()" style="background: transparent; color: white; border: 1px solid white; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-left: 8px;">Later</button>
        `;

        document.body.appendChild(notification);
    }

    // Show installed notification
    showInstalledNotification() {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #4CAF50;
            color: white;
            padding: 16px 24px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1001;
        `;

        notification.textContent = '🎉 App installed successfully!';
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize PWA Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});

// Export for use in other scripts
window.PWAManager = PWAManager;
