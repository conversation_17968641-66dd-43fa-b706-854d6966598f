/**
 * Market Trends JavaScript
 * Handles data fetching, chart rendering, and UI interactions for the market trends page
 */

// Global variables
let marketData = [];
let priceChart = null;
let comparisonChart = null;
let detailsChart = null;
let selectedCrop = 'all';
let selectedLocation = 'all';
let selectedTimeRange = '7days';
let chartType = 'line';
let userAlerts = [];

// DOM elements
const cropSelect = document.getElementById('cropSelect');
const locationSelect = document.getElementById('locationSelect');
const timeRangeSelect = document.getElementById('timeRangeSelect');
const filterForm = document.getElementById('filterForm');
const marketPriceTableBody = document.getElementById('marketPriceTableBody');
const priceChartElement = document.getElementById('priceChart');
const marketComparisonChartElement = document.getElementById('marketComparisonChart');
const detailsChartElement = document.getElementById('detailsChart');
const lineChartBtn = document.getElementById('lineChartBtn');
const barChartBtn = document.getElementById('barChartBtn');
const refreshDataBtn = document.getElementById('refreshData');
const alertsContainer = document.getElementById('alertsContainer');
const noAlertsMessage = document.getElementById('noAlertsMessage');
const alertForm = document.getElementById('alertForm');
const alertCropSelect = document.getElementById('alertCrop');
const alertConditionSelect = document.getElementById('alertCondition');
const alertPriceInput = document.getElementById('alertPrice');
const saveAlertBtn = document.getElementById('saveAlertBtn');

// Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
  // Load initial data
  await Promise.all([
    loadCrops(),
    loadLocations()
  ]);

  // Load market data
  await loadMarketData();

  // Load user alerts from localStorage
  loadUserAlerts();

  // Set up event listeners
  setupEventListeners();
});

// Set up event listeners
function setupEventListeners() {
  // Filter form submission
  filterForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    selectedCrop = cropSelect.value;
    selectedLocation = locationSelect.value;
    selectedTimeRange = timeRangeSelect.value;
    await loadMarketData();
  });

  // Chart type toggle
  lineChartBtn.addEventListener('click', () => {
    chartType = 'line';
    lineChartBtn.classList.add('active');
    barChartBtn.classList.remove('active');
    renderPriceChart();
  });

  barChartBtn.addEventListener('click', () => {
    chartType = 'bar';
    barChartBtn.classList.add('active');
    lineChartBtn.classList.remove('active');
    renderPriceChart();
  });

  // Refresh data button
  refreshDataBtn.addEventListener('click', async () => {
    refreshDataBtn.disabled = true;
    refreshDataBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
    await loadMarketData();
    refreshDataBtn.disabled = false;
    refreshDataBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Refresh';
  });

  // Save alert button
  saveAlertBtn.addEventListener('click', saveAlert);
}

// Load available crops
async function loadCrops() {
  try {
    const response = await fetch('/market-trends/api/crops');
    if (!response.ok) throw new Error('Failed to fetch crops');

    const crops = await response.json();

    // Populate crop select dropdown
    cropSelect.innerHTML = '<option value="all" selected>All Crops</option>';
    alertCropSelect.innerHTML = '<option value="" selected disabled>Select a crop</option>';

    crops.forEach(crop => {
      cropSelect.innerHTML += `<option value="${crop.id}">${crop.name}</option>`;
      alertCropSelect.innerHTML += `<option value="${crop.id}">${crop.name}</option>`;
    });
  } catch (error) {
    console.error('Error loading crops:', error);

    // Show error in dropdown
    cropSelect.innerHTML = '<option value="all" selected>Error loading crops</option>';
    alertCropSelect.innerHTML = '<option value="" selected disabled>Error loading crops</option>';
  }
}

// Load available market locations
async function loadLocations() {
  try {
    const response = await fetch('/market-trends/api/locations');
    if (!response.ok) throw new Error('Failed to fetch locations');

    const locations = await response.json();

    // Populate location select dropdown
    locationSelect.innerHTML = '<option value="all" selected>All Locations</option>';

    locations.forEach(location => {
      locationSelect.innerHTML += `<option value="${location.id}">${location.name}</option>`;
    });
  } catch (error) {
    console.error('Error loading locations:', error);

    // Show error in dropdown
    locationSelect.innerHTML = '<option value="all" selected>Error loading locations</option>';
  }
}

// Load market data based on selected filters
async function loadMarketData() {
  try {
    // Show loading state
    marketPriceTableBody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Loading market data...</p>
        </td>
      </tr>
    `;

    // Fetch data from API
    const response = await fetch(`/market-trends/api/data?crop=${selectedCrop}&location=${selectedLocation}&timeRange=${selectedTimeRange}`);
    if (!response.ok) throw new Error('Failed to fetch market data');

    marketData = await response.json();

    // Render the data
    renderMarketTable();
    renderPriceChart();
    renderComparisonChart();

    // Check alerts against new data
    checkAlerts();
  } catch (error) {
    console.error('Error loading market data:', error);

    // Show error message
    marketPriceTableBody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center py-4">
          <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
          <p class="mt-2">Failed to load market data. Please check your connection and try again.</p>
        </td>
      </tr>
    `;
  }
}

// Render market data table
function renderMarketTable() {
  marketPriceTableBody.innerHTML = '';

  // Format crop and location names for display
  const formatName = (str) => str.charAt(0).toUpperCase() + str.slice(1).replace(/-/g, ' ');

  marketData.forEach(item => {
    const row = document.createElement('tr');

    // Format price change with color
    const priceChangeClass = item.priceChange > 0 ? 'text-success' : (item.priceChange < 0 ? 'text-danger' : 'text-muted');
    const priceChangeIcon = item.priceChange > 0 ? 'bi-arrow-up' : (item.priceChange < 0 ? 'bi-arrow-down' : 'bi-dash');

    row.innerHTML = `
      <td>${formatName(item.crop)}</td>
      <td>${formatName(item.location)}</td>
      <td>$${item.currentPrice.toFixed(2)}</td>
      <td class="${priceChangeClass}">
        <i class="bi ${priceChangeIcon}"></i>
        ${Math.abs(item.priceChange).toFixed(2)}%
      </td>
      <td>${new Date(item.lastUpdated).toLocaleString()}</td>
      <td>
        <button class="btn btn-sm btn-outline-primary view-details-btn" data-crop="${item.crop}" data-location="${item.location}">
          <i class="bi bi-graph-up"></i>
        </button>
        <button class="btn btn-sm btn-outline-secondary set-alert-btn" data-crop="${item.crop}" data-price="${item.currentPrice}">
          <i class="bi bi-bell"></i>
        </button>
      </td>
    `;

    marketPriceTableBody.appendChild(row);
  });

  // Add event listeners to the buttons
  document.querySelectorAll('.view-details-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const crop = btn.getAttribute('data-crop');
      const location = btn.getAttribute('data-location');
      showMarketDetails(crop, location);
    });
  });

  document.querySelectorAll('.set-alert-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const crop = btn.getAttribute('data-crop');
      const price = parseFloat(btn.getAttribute('data-price'));

      // Pre-fill the alert form
      alertCropSelect.value = crop;
      alertPriceInput.value = price.toFixed(2);

      // Show the modal
      const modal = new bootstrap.Modal(document.getElementById('addAlertModal'));
      modal.show();
    });
  });
}

// Render price trend chart
function renderPriceChart() {
  // Destroy existing chart if it exists
  if (priceChart) {
    priceChart.destroy();
  }

  // Filter data based on selected crop and location
  let filteredData = marketData;
  if (selectedCrop !== 'all') {
    filteredData = filteredData.filter(item => item.crop === selectedCrop);
  }
  if (selectedLocation !== 'all') {
    filteredData = filteredData.filter(item => item.location === selectedLocation);
  }

  // If we have too many data points, limit to a reasonable number
  if (filteredData.length > 8) {
    // If a specific crop is selected, show all locations
    // If a specific location is selected, show all crops
    // If neither is selected, show the top crops by price
    if (selectedCrop !== 'all') {
      // Keep all locations for the selected crop
    } else if (selectedLocation !== 'all') {
      // Keep all crops for the selected location
    } else {
      // Limit to top 8 crops by current price
      filteredData = filteredData
        .sort((a, b) => b.currentPrice - a.currentPrice)
        .slice(0, 8);
    }
  }

  // Prepare data for the chart
  const labels = filteredData[0]?.timeSeriesData.map(point => point.date) || [];
  const datasets = filteredData.map(item => {
    // Generate a color based on the crop name
    const hue = Math.abs(item.crop.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 360);
    const color = `hsl(${hue}, 70%, 50%)`;

    return {
      label: `${item.crop.charAt(0).toUpperCase() + item.crop.slice(1)} (${item.location.charAt(0).toUpperCase() + item.location.slice(1)})`,
      data: item.timeSeriesData.map(point => point.price),
      borderColor: color,
      backgroundColor: `${color}33`, // Add transparency
      tension: 0.3,
      pointRadius: 3,
      pointHoverRadius: 5
    };
  });

  // Create the chart
  const ctx = priceChartElement.getContext('2d');
  priceChart = new Chart(ctx, {
    type: chartType,
    data: {
      labels: labels,
      datasets: datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: $${context.raw.toFixed(2)}`;
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Date'
          }
        },
        y: {
          title: {
            display: true,
            text: 'Price (USD)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value.toFixed(2);
            }
          }
        }
      }
    }
  });
}

// Render market comparison chart
function renderComparisonChart() {
  // Destroy existing chart if it exists
  if (comparisonChart) {
    comparisonChart.destroy();
  }

  // Filter data based on selected crop and location
  let filteredData = marketData;
  if (selectedCrop !== 'all') {
    filteredData = filteredData.filter(item => item.crop === selectedCrop);
  }
  if (selectedLocation !== 'all') {
    filteredData = filteredData.filter(item => item.location === selectedLocation);
  }

  // If we have too many data points, limit to a reasonable number
  if (filteredData.length > 5) {
    // If a specific crop is selected, show all locations
    // If a specific location is selected, show all crops
    // If neither is selected, show the top crops by price
    if (selectedCrop !== 'all') {
      // Keep all locations for the selected crop
    } else if (selectedLocation !== 'all') {
      // Keep all crops for the selected location
    } else {
      // Limit to top 5 crops by current price
      filteredData = filteredData
        .sort((a, b) => b.currentPrice - a.currentPrice)
        .slice(0, 5);
    }
  }

  // Prepare data for the chart
  const labels = filteredData.map(item => {
    const cropName = item.crop.charAt(0).toUpperCase() + item.crop.slice(1);
    const locationName = item.location.charAt(0).toUpperCase() + item.location.slice(1);
    return `${cropName} (${locationName})`;
  });

  const currentPrices = filteredData.map(item => item.currentPrice);
  const priceChanges = filteredData.map(item => item.priceChange);

  // Create the chart
  const ctx = marketComparisonChartElement.getContext('2d');
  comparisonChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [
        {
          label: 'Current Price ($)',
          data: currentPrices,
          backgroundColor: 'rgba(76, 175, 80, 0.7)',
          borderColor: 'rgba(76, 175, 80, 1)',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      indexAxis: 'y',
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `Price: $${context.raw.toFixed(2)}`;
            },
            afterLabel: function(context) {
              const index = context.dataIndex;
              const change = priceChanges[index];
              const changeText = change > 0 ? `+${change.toFixed(2)}%` : `${change.toFixed(2)}%`;
              return `Change: ${changeText}`;
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Price (USD)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value.toFixed(2);
            }
          }
        }
      }
    }
  });
}

// Show market details modal
function showMarketDetails(crop, location) {
  // Find the data for the selected crop and location
  const item = marketData.find(d => d.crop === crop && d.location === location);
  if (!item) return;

  // Update modal title
  const cropName = crop.charAt(0).toUpperCase() + crop.slice(1);
  const locationName = location.charAt(0).toUpperCase() + location.slice(1);
  document.getElementById('marketDetailsModalLabel').textContent = `${cropName} Market Details - ${locationName}`;

  // Render details chart
  renderDetailsChart(item);

  // Populate price statistics table
  const priceStatsTable = document.getElementById('priceStatsTable');
  const timeSeriesData = item.timeSeriesData;
  const prices = timeSeriesData.map(point => point.price);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);
  const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

  priceStatsTable.innerHTML = `
    <tr>
      <th>Current Price:</th>
      <td>$${item.currentPrice.toFixed(2)}</td>
    </tr>
    <tr>
      <th>Price Change:</th>
      <td class="${item.priceChange > 0 ? 'text-success' : (item.priceChange < 0 ? 'text-danger' : 'text-muted')}">
        ${item.priceChange > 0 ? '+' : ''}${item.priceChange.toFixed(2)}%
      </td>
    </tr>
    <tr>
      <th>Minimum Price:</th>
      <td>$${minPrice.toFixed(2)}</td>
    </tr>
    <tr>
      <th>Maximum Price:</th>
      <td>$${maxPrice.toFixed(2)}</td>
    </tr>
    <tr>
      <th>Average Price:</th>
      <td>$${avgPrice.toFixed(2)}</td>
    </tr>
  `;

  // Populate market information table
  const marketInfoTable = document.getElementById('marketInfoTable');
  marketInfoTable.innerHTML = `
    <tr>
      <th>Market:</th>
      <td>${locationName}</td>
    </tr>
    <tr>
      <th>Crop:</th>
      <td>${cropName}</td>
    </tr>
    <tr>
      <th>Last Updated:</th>
      <td>${new Date(item.lastUpdated).toLocaleString()}</td>
    </tr>
    <tr>
      <th>Time Range:</th>
      <td>${timeRangeSelect.options[timeRangeSelect.selectedIndex].text}</td>
    </tr>
  `;

  // Show the modal
  const modal = new bootstrap.Modal(document.getElementById('marketDetailsModal'));
  modal.show();
}

// Render details chart
function renderDetailsChart(item) {
  // Destroy existing chart if it exists
  if (detailsChart) {
    detailsChart.destroy();
  }

  // Prepare data for the chart
  const labels = item.timeSeriesData.map(point => point.date);
  const prices = item.timeSeriesData.map(point => point.price);

  // Generate a color based on the crop name
  const hue = Math.abs(item.crop.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 360);
  const color = `hsl(${hue}, 70%, 50%)`;

  // Create the chart
  const ctx = detailsChartElement.getContext('2d');
  detailsChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [
        {
          label: 'Price',
          data: prices,
          borderColor: color,
          backgroundColor: `${color}33`, // Add transparency
          fill: true,
          tension: 0.3,
          pointRadius: 4,
          pointHoverRadius: 6
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `Price: $${context.raw.toFixed(2)}`;
            }
          }
        }
      },
      scales: {
        y: {
          title: {
            display: true,
            text: 'Price (USD)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value.toFixed(2);
            }
          }
        }
      }
    }
  });
}

// Load user alerts from localStorage
function loadUserAlerts() {
  const savedAlerts = localStorage.getItem('marketTrends_alerts');
  if (savedAlerts) {
    userAlerts = JSON.parse(savedAlerts);
    renderAlerts();
  }
}

// Save a new price alert
function saveAlert() {
  const crop = alertCropSelect.value;
  const condition = alertConditionSelect.value;
  const price = parseFloat(alertPriceInput.value);

  if (!crop || isNaN(price) || price <= 0) {
    // Show validation error
    alert('Please fill in all fields correctly.');
    return;
  }

  // Create a unique ID for the alert
  const alertId = Date.now().toString();

  // Create the alert object
  const alert = {
    id: alertId,
    crop,
    condition,
    price,
    createdAt: new Date().toISOString()
  };

  // Add to alerts array
  userAlerts.push(alert);

  // Save to localStorage
  localStorage.setItem('marketTrends_alerts', JSON.stringify(userAlerts));

  // Render alerts
  renderAlerts();

  // Close the modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('addAlertModal'));
  modal.hide();

  // Reset form
  alertForm.reset();

  // Check if the alert is already triggered
  checkAlerts();
}

// Render user alerts
function renderAlerts() {
  if (userAlerts.length === 0) {
    noAlertsMessage.style.display = 'block';
    alertsContainer.innerHTML = '';
    return;
  }

  noAlertsMessage.style.display = 'none';
  alertsContainer.innerHTML = '';

  // Format crop names for display
  const formatName = (str) => str.charAt(0).toUpperCase() + str.slice(1).replace(/-/g, ' ');

  userAlerts.forEach(alert => {
    const alertElement = document.createElement('div');
    alertElement.className = 'alert alert-light d-flex justify-content-between align-items-center';
    alertElement.innerHTML = `
      <div>
        <i class="bi bi-bell-fill me-2 text-warning"></i>
        Alert me when <strong>${formatName(alert.crop)}</strong> price goes
        <strong>${alert.condition === 'above' ? 'above' : 'below'} $${alert.price.toFixed(2)}</strong>
      </div>
      <button class="btn btn-sm btn-outline-danger delete-alert-btn" data-alert-id="${alert.id}">
        <i class="bi bi-trash"></i>
      </button>
    `;

    alertsContainer.appendChild(alertElement);
  });

  // Add event listeners to delete buttons
  document.querySelectorAll('.delete-alert-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const alertId = btn.getAttribute('data-alert-id');
      deleteAlert(alertId);
    });
  });
}

// Delete an alert
function deleteAlert(alertId) {
  userAlerts = userAlerts.filter(alert => alert.id !== alertId);
  localStorage.setItem('marketTrends_alerts', JSON.stringify(userAlerts));
  renderAlerts();
}

// Check if any alerts are triggered
function checkAlerts() {
  if (userAlerts.length === 0 || marketData.length === 0) return;

  userAlerts.forEach(alert => {
    // Find current price for the crop
    const cropData = marketData.filter(item => item.crop === alert.crop);
    if (cropData.length === 0) return;

    // Check each market location
    cropData.forEach(item => {
      const currentPrice = item.currentPrice;
      const locationName = item.location.charAt(0).toUpperCase() + item.location.slice(1).replace(/-/g, ' ');
      const cropName = alert.crop.charAt(0).toUpperCase() + alert.crop.slice(1).replace(/-/g, ' ');

      // Check if alert condition is met
      if (
        (alert.condition === 'above' && currentPrice > alert.price) ||
        (alert.condition === 'below' && currentPrice < alert.price)
      ) {
        // Show notification
        showNotification(
          `Price Alert: ${cropName}`,
          `${cropName} price in ${locationName} is now ${alert.condition === 'above' ? 'above' : 'below'} $${alert.price.toFixed(2)} (Current: $${currentPrice.toFixed(2)})`
        );
      }
    });
  });
}

// Show a notification
function showNotification(title, message) {
  // Check if the browser supports notifications
  if (!("Notification" in window)) {
    alert(`${title}: ${message}`);
    return;
  }

  // Check if permission is already granted
  if (Notification.permission === "granted") {
    const notification = new Notification(title, {
      body: message,
      icon: '/img/notification-icon.png'
    });

    // Close the notification after 5 seconds
    setTimeout(() => notification.close(), 5000);
  }
  // Otherwise, request permission
  else if (Notification.permission !== "denied") {
    Notification.requestPermission().then(permission => {
      if (permission === "granted") {
        const notification = new Notification(title, {
          body: message,
          icon: '/img/notification-icon.png'
        });

        // Close the notification after 5 seconds
        setTimeout(() => notification.close(), 5000);
      }
    });
  }
}